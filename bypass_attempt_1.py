#!/usr/bin/env python3
# 绕过尝试 1: 修改第一个字节绕过
# 修改版本/类型字段来改变验证分支

import struct
import shutil

def bypass_attempt_1():
    file_path = r"F:\刷机桌面\过校验\dump\uboot"
    
    print("🚀 绕过尝试 1: 修改第一个字节绕过")
    print("修改版本/类型字段来改变验证分支")
    
    # 备份
    shutil.copy2(file_path, file_path + '.backup_1')
    print("✅ 已创建备份")
    
    # 读取文件
    with open(file_path, 'rb') as f:
        data = bytearray(f.read())
    
    # 执行修改
    # 修改第一个字节
    data[531536] = 0
    print(f"🔧 修改第一个字节: {data[531536]} -> 0")
    
    # 保存
    with open(file_path + '.attempt_1', 'wb') as f:
        f.write(data)
    
    print("✅ 修改完成!")
    print(f"📁 输出文件: {file_path}.attempt_1")

if __name__ == "__main__":
    bypass_attempt_1()
