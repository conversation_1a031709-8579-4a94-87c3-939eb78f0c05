#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于DHTBDump结果的UBOOT分析工具
根据dump信息精确定位和修改签名字段

使用方法: python analyze_dump_result.py <uboot_file>
"""

import struct
import sys
import os

def analyze_with_dump_info(file_path):
    """基于dump信息分析文件"""
    print(f"🔍 分析文件: {file_path}")
    
    # DHTBDump提供的信息
    dump_info = {
        'file_size': 0x81ea4,      # 532132
        'payload_size': 0x819f0,   # 530928  
        'cert_size': 0x254,        # 596
        'cert_offset': 0x81c50,    # 531536
        'cert_type': 0x1,
        'key_bit_len': 0x800       # 2048位
    }
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        
        actual_size = len(data)
        print(f"✅ 文件读取成功")
        print(f"📏 实际文件大小: {actual_size} (0x{actual_size:x})")
        print(f"📏 Dump显示大小: {dump_info['file_size']} (0x{dump_info['file_size']:x})")
        
        if actual_size != dump_info['file_size']:
            print("⚠️  文件大小不匹配，可能是不同的文件")
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return
    
    print(f"\n🎯 === 基于Dump信息的分析 ===")
    print(f"载荷大小: {dump_info['payload_size']} (0x{dump_info['payload_size']:x})")
    print(f"证书大小: {dump_info['cert_size']} (0x{dump_info['cert_size']:x})")
    print(f"证书偏移: {dump_info['cert_offset']} (0x{dump_info['cert_offset']:x})")
    print(f"密钥长度: {dump_info['key_bit_len']}位")
    
    # 关键分析
    print(f"\n🔍 === 关键发现 ===")
    
    if dump_info['cert_size'] == 596:
        print("🎯 证书大小是596字节 - 这是RSA-3072的签名长度！")
        print("💡 可能的情况:")
        print("   1. 文件已经被修改过（从1024改为596）")
        print("   2. 原始就是RSA-3072签名")
        print("   3. 这是一个特殊的签名格式")
    
    if dump_info['key_bit_len'] == 0x800:  # 2048位
        print("🔍 密钥长度是2048位，但证书大小是596字节")
        print("⚠️  这个组合很可疑，可能存在不一致")
    
    # 检查证书区域
    cert_offset = dump_info['cert_offset']
    cert_size = dump_info['cert_size']
    
    if cert_offset + cert_size <= len(data):
        print(f"\n📋 === 证书区域分析 ===")
        cert_data = data[cert_offset:cert_offset + cert_size]
        
        print(f"证书区域前32字节:")
        for i in range(0, min(32, len(cert_data)), 16):
            hex_line = ' '.join(f'{b:02x}' for b in cert_data[i:i+16])
            print(f"  {cert_offset + i:04x}: {hex_line}")
        
        # 在证书区域搜索签名长度字段
        search_in_cert_area(cert_data, cert_offset)
    
    # 全文搜索关键值
    search_signature_values(data)
    
    # 生成修改建议
    generate_modification_advice(data, dump_info)

def search_in_cert_area(cert_data, base_offset):
    """在证书区域搜索签名长度字段"""
    print(f"\n🔍 === 证书区域内搜索 ===")
    
    # 搜索596 (0x254)
    pattern_596 = struct.pack('<Q', 596)
    pos = cert_data.find(pattern_596)
    if pos != -1:
        abs_pos = base_offset + pos
        print(f"✅ 在证书区域找到596值，位置: 0x{abs_pos:x}")
    
    # 搜索1024 (0x400)  
    pattern_1024 = struct.pack('<Q', 1024)
    pos = cert_data.find(pattern_1024)
    if pos != -1:
        abs_pos = base_offset + pos
        print(f"✅ 在证书区域找到1024值，位置: 0x{abs_pos:x}")
    
    # 搜索2048 (0x800)
    pattern_2048 = struct.pack('<Q', 2048)
    pos = cert_data.find(pattern_2048)
    if pos != -1:
        abs_pos = base_offset + pos
        print(f"✅ 在证书区域找到2048值，位置: 0x{abs_pos:x}")

def search_signature_values(data):
    """搜索签名相关的值"""
    print(f"\n🔍 === 全文搜索签名值 ===")
    
    values_to_search = {
        596: "RSA-3072签名长度",
        1024: "RSA-4096签名长度", 
        2048: "可能的密钥长度",
        4096: "RSA-4096密钥长度"
    }
    
    for value, desc in values_to_search.items():
        # 8字节小端序
        pattern_8 = struct.pack('<Q', value)
        # 4字节小端序
        pattern_4 = struct.pack('<I', value)
        
        for pattern, size_desc in [(pattern_8, "8字节"), (pattern_4, "4字节")]:
            pos = 0
            count = 0
            while True:
                pos = data.find(pattern, pos)
                if pos == -1:
                    break
                
                if count == 0:
                    print(f"\n🎯 {desc} ({size_desc}):")
                
                print(f"   位置: 0x{pos:x} ({pos})")
                
                # 显示周围数据
                start = max(0, pos - 8)
                end = min(len(data), pos + len(pattern) + 8)
                hex_data = ' '.join(f'{b:02x}' for b in data[start:end])
                print(f"   数据: {hex_data}")
                
                count += 1
                pos += 1
                
                if count >= 2:  # 最多显示2个
                    break

def generate_modification_advice(data, dump_info):
    """生成修改建议"""
    print(f"\n💡 === 修改建议 ===")
    
    # 如果证书大小已经是596
    if dump_info['cert_size'] == 596:
        print("🎯 当前证书大小已经是596字节")
        print("📝 可能的情况和建议:")
        print("   1. 如果想要绕过验证:")
        print("      - 文件可能已经被修改过")
        print("      - 尝试修改UBOOT代码部分，看是否能绕过验证")
        print("   2. 如果想要恢复原始状态:")
        print("      - 需要将596改回1024")
        print("      - 搜索596的位置并改为1024")
        
        # 搜索596并提供修改为1024的方案
        pattern_596 = struct.pack('<Q', 596)
        pos = data.find(pattern_596)
        if pos != -1:
            print(f"\n🔧 恢复原始状态的修改:")
            print(f"   位置: 0x{pos:x}")
            print(f"   原值: 54 02 00 00 00 00 00 00 (596)")
            print(f"   新值: 00 04 00 00 00 00 00 00 (1024)")
            
            generate_restore_script(data, pos)
    
    else:
        print("🎯 证书大小不是596，可能是原始文件")
        print("📝 绕过验证的修改建议:")
        
        # 搜索1024并提供修改为596的方案
        pattern_1024 = struct.pack('<Q', 1024)
        pos = data.find(pattern_1024)
        if pos != -1:
            print(f"\n🔧 绕过验证的修改:")
            print(f"   位置: 0x{pos:x}")
            print(f"   原值: 00 04 00 00 00 00 00 00 (1024)")
            print(f"   新值: 54 02 00 00 00 00 00 00 (596)")
            
            generate_bypass_script(data, pos)

def generate_bypass_script(data, pos):
    """生成绕过脚本"""
    script = f'''#!/usr/bin/env python3
# 绕过验证脚本 - 将1024改为596

import struct
import shutil

def bypass_verification():
    file_path = input("请输入UBOOT文件路径: ").strip('"')
    
    # 备份
    shutil.copy2(file_path, file_path + '.original')
    print("✅ 已创建备份")
    
    # 读取文件
    with open(file_path, 'rb') as f:
        data = bytearray(f.read())
    
    # 修改位置 0x{pos:x}
    offset = 0x{pos:x}
    data[offset:offset+8] = struct.pack('<Q', 596)
    
    # 保存
    with open(file_path + '.bypassed', 'wb') as f:
        f.write(data)
    
    print("✅ 绕过修改完成!")
    print("现在可以任意修改UBOOT代码了")

if __name__ == "__main__":
    bypass_verification()
'''
    
    with open('bypass_script.py', 'w', encoding='utf-8') as f:
        f.write(script)
    
    print("🛠️ 已生成绕过脚本: bypass_script.py")

def generate_restore_script(data, pos):
    """生成恢复脚本"""
    script = f'''#!/usr/bin/env python3
# 恢复原始状态脚本 - 将596改为1024

import struct
import shutil

def restore_original():
    file_path = input("请输入UBOOT文件路径: ").strip('"')
    
    # 备份
    shutil.copy2(file_path, file_path + '.before_restore')
    print("✅ 已创建备份")
    
    # 读取文件
    with open(file_path, 'rb') as f:
        data = bytearray(f.read())
    
    # 修改位置 0x{pos:x}
    offset = 0x{pos:x}
    data[offset:offset+8] = struct.pack('<Q', 1024)
    
    # 保存
    with open(file_path + '.restored', 'wb') as f:
        f.write(data)
    
    print("✅ 恢复原始状态完成!")

if __name__ == "__main__":
    restore_original()
'''
    
    with open('restore_script.py', 'w', encoding='utf-8') as f:
        f.write(script)
    
    print("🛠️ 已生成恢复脚本: restore_script.py")

def main():
    if len(sys.argv) != 2:
        print('使用方法: python analyze_dump_result.py "文件路径"')
        print('\n示例:')
        print('  python analyze_dump_result.py "F:\\刷机桌面\\过校验\\dump\\uboot - 副本"')
        return 1
    
    file_path = sys.argv[1].strip('"\'')
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return 1
    
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║            基于DHTBDump结果的UBOOT分析工具                   ║")
    print("║                                                              ║")
    print("║  根据dump信息精确分析签名字段位置和修改方案                  ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print()
    
    analyze_with_dump_info(file_path)
    return 0

if __name__ == "__main__":
    sys.exit(main())
