#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RSA-3072原始文件分析工具
针对cert size为596的原始uboot文件进行漏洞分析

使用方法: python rsa3072_analysis.py <uboot_file>
"""

import struct
import sys
import os

def analyze_rsa3072_file(file_path):
    """分析RSA-3072原始文件"""
    print(f"🔍 分析RSA-3072原始文件: {file_path}")
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        print(f"✅ 文件大小: {len(data)} 字节 (0x{len(data):x})")
    except Exception as e:
        print(f"❌ 读取失败: {e}")
        return
    
    # 基于DHTBDump的信息
    dump_info = {
        'file_size': 0x81ea4,      # 532132
        'payload_size': 0x819f0,   # 530928  
        'cert_size': 0x254,        # 596 - RSA-3072
        'cert_offset': 0x81c50,    # 531536
        'key_bit_len': 0x800       # 2048位
    }
    
    print(f"\n📋 === 原始文件信息 ===")
    print(f"证书大小: {dump_info['cert_size']} 字节 (RSA-3072)")
    print(f"证书偏移: 0x{dump_info['cert_offset']:x}")
    print(f"密钥长度: {dump_info['key_bit_len']}位")
    
    # 计算签名长度字段位置
    cert_offset = dump_info['cert_offset']
    sig_len_field_offset = cert_offset + 0x220  # 根据IDA分析的偏移
    
    print(f"\n🎯 === 签名长度字段分析 ===")
    print(f"预计签名长度字段位置: 0x{sig_len_field_offset:x}")
    
    if sig_len_field_offset + 8 <= len(data):
        # 读取当前签名长度字段值
        current_sig_len = struct.unpack('<Q', data[sig_len_field_offset:sig_len_field_offset+8])[0]
        print(f"当前签名长度字段值: {current_sig_len} (0x{current_sig_len:x})")
        
        if current_sig_len == 596:
            print("✅ 确认：这是RSA-3072签名 (596字节)")
            print("\n💡 === 反向漏洞利用分析 ===")
            print("原理：将596改为1024，让SPL认为这是RSA-4096签名")
            print("效果：哈希计算位置从+300字节变为+556字节")
            print("结果：哈希验证范围向后偏移256字节，跳过实际代码")
            
            analyze_reverse_exploit(data, sig_len_field_offset)
        else:
            print(f"⚠️  意外的签名长度值: {current_sig_len}")
    else:
        print("❌ 签名长度字段位置超出文件范围")
    
    # 搜索所有596的位置
    search_all_596_positions(data)

def analyze_reverse_exploit(data, sig_len_offset):
    """分析反向漏洞利用"""
    print(f"\n🔧 === 反向漏洞利用方案 ===")
    
    print("📝 修改方案:")
    print(f"   位置: 0x{sig_len_offset:x}")
    print(f"   原值: 54 02 00 00 00 00 00 00  (596, RSA-3072)")
    print(f"   新值: 00 04 00 00 00 00 00 00  (1024, 伪造RSA-4096)")
    
    print("\n🎯 漏洞效果:")
    print("1. SPL读取到签名长度1024")
    print("2. 认为这是RSA-4096签名")
    print("3. 哈希计算从签名数据+556字节开始")
    print("4. 实际签名数据只有596字节，验证范围错位")
    print("5. 可以修改前面的UBOOT代码而不影响验证")
    
    print("\n⚠️  注意事项:")
    print("- 这种修改可能导致签名验证失败")
    print("- 需要确保新的哈希计算范围内有合适的数据")
    print("- 建议先在测试环境验证")
    
    generate_reverse_exploit_script(sig_len_offset)

def search_all_596_positions(data):
    """搜索所有596的位置"""
    print(f"\n🔍 === 搜索所有596值的位置 ===")
    
    # 8字节小端序
    pattern_596_8 = struct.pack('<Q', 596)
    # 4字节小端序  
    pattern_596_4 = struct.pack('<I', 596)
    
    patterns = [
        (pattern_596_8, "8字节小端序"),
        (pattern_596_4, "4字节小端序")
    ]
    
    for pattern, desc in patterns:
        pos = 0
        count = 0
        print(f"\n🎯 搜索596 ({desc}):")
        
        while True:
            pos = data.find(pattern, pos)
            if pos == -1:
                break
            
            print(f"   位置 {count+1}: 0x{pos:x} ({pos})")
            
            # 显示周围数据
            start = max(0, pos - 8)
            end = min(len(data), pos + len(pattern) + 8)
            hex_data = ' '.join(f'{b:02x}' for b in data[start:end])
            print(f"   数据: {hex_data}")
            
            count += 1
            pos += 1
            
            if count >= 3:  # 最多显示3个
                break
        
        if count == 0:
            print(f"   未找到596 ({desc})")

def generate_reverse_exploit_script(sig_len_offset):
    """生成反向漏洞利用脚本"""
    script = f'''#!/usr/bin/env python3
# RSA-3072反向漏洞利用脚本
# 将596改为1024，操控哈希计算范围

import struct
import shutil

def reverse_exploit():
    file_path = input("请输入UBOOT文件路径: ").strip('"')
    
    print("🚀 开始RSA-3072反向漏洞利用...")
    
    # 创建备份
    backup_path = file_path + '.original'
    shutil.copy2(file_path, backup_path)
    print(f"✅ 已创建备份: {{backup_path}}")
    
    # 读取文件
    with open(file_path, 'rb') as f:
        data = bytearray(f.read())
    
    # 修改签名长度字段
    offset = 0x{sig_len_offset:x}
    
    # 验证当前值
    current_value = struct.unpack('<Q', data[offset:offset+8])[0]
    print(f"🔍 当前签名长度: {{current_value}}")
    
    if current_value != 596:
        print("❌ 当前值不是596，可能文件已被修改")
        return
    
    # 修改为1024 (伪造RSA-4096)
    new_value = 1024
    data[offset:offset+8] = struct.pack('<Q', new_value)
    
    print(f"🔧 修改签名长度: 596 -> {{new_value}}")
    
    # 保存修改后的文件
    output_path = file_path + '.exploited'
    with open(output_path, 'wb') as f:
        f.write(data)
    
    print(f"✅ 反向漏洞利用完成!")
    print(f"📁 原文件: {{file_path}}")
    print(f"📁 备份文件: {{backup_path}}")
    print(f"📁 利用文件: {{output_path}}")
    
    print(f"\\n💡 现在可以:")
    print(f"1. 修改UBOOT代码的某些部分")
    print(f"2. 哈希验证范围已被操控")
    print(f"3. SPL会认为这是RSA-4096签名")
    
    # 验证修改
    with open(output_path, 'rb') as f:
        verify_data = f.read()
    verify_value = struct.unpack('<Q', verify_data[offset:offset+8])[0]
    
    if verify_value == new_value:
        print("✅ 修改验证成功!")
    else:
        print("❌ 修改验证失败!")

if __name__ == "__main__":
    reverse_exploit()
'''
    
    with open('reverse_exploit.py', 'w', encoding='utf-8') as f:
        f.write(script)
    
    print(f"\n🛠️ 已生成反向漏洞利用脚本: reverse_exploit.py")
    print("🚀 运行命令: python reverse_exploit.py")

def main():
    if len(sys.argv) != 2:
        print('使用方法: python rsa3072_analysis.py "文件路径"')
        print('\n示例:')
        print('  python rsa3072_analysis.py "F:\\刷机桌面\\过校验\\dump\\uboot - 副本"')
        return 1
    
    file_path = sys.argv[1].strip('"\'')
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return 1
    
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║              RSA-3072原始文件漏洞分析工具                    ║")
    print("║                                                              ║")
    print("║  针对cert size为596的原始文件进行反向漏洞利用分析            ║")
    print("║  将RSA-3072伪造为RSA-4096来操控哈希计算范围                  ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print()
    
    analyze_rsa3072_file(file_path)
    return 0

if __name__ == "__main__":
    sys.exit(main())
