#!/usr/bin/env python3
# 绕过尝试 2: 修改偏移556绕过
# 修改RSA验证检查字段

import struct
import shutil

def bypass_attempt_2():
    file_path = r"F:\刷机桌面\过校验\dump\uboot"
    
    print("🚀 绕过尝试 2: 修改偏移556绕过")
    print("修改RSA验证检查字段")
    
    # 备份
    shutil.copy2(file_path, file_path + '.backup_2')
    print("✅ 已创建备份")
    
    # 读取文件
    with open(file_path, 'rb') as f:
        data = bytearray(f.read())
    
    # 执行修改
    # 修改偏移556
    data[532092:532096] = struct.pack('<I', 1)
    print(f"🔧 修改偏移556: 新值 1")
    
    # 保存
    with open(file_path + '.attempt_2', 'wb') as f:
        f.write(data)
    
    print("✅ 修改完成!")
    print(f"📁 输出文件: {file_path}.attempt_2")

if __name__ == "__main__":
    bypass_attempt_2()
