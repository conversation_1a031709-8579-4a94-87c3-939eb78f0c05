#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UBOOT文件结构分析工具
专门用于分析和定位签名字段位置

使用方法: python uboot_analyzer.py <uboot_file>
"""

import struct
import sys
import os
from pathlib import Path

class UBootAnalyzer:
    """UBOOT文件分析器"""
    
    def __init__(self, file_path):
        self.file_path = Path(file_path)
        self.data = None
        self.file_size = 0
        
    def load_file(self):
        """加载文件"""
        try:
            with open(self.file_path, 'rb') as f:
                self.data = f.read()
            self.file_size = len(self.data)
            print(f"✅ 文件加载成功: {self.file_path}")
            print(f"📏 文件大小: {self.file_size} 字节 (0x{self.file_size:x})")
            return True
        except Exception as e:
            print(f"❌ 文件加载失败: {e}")
            return False
    
    def analyze_header(self):
        """分析文件头部"""
        print("\n🔍 === 文件头部分析 ===")
        
        # 显示前512字节的十六进制
        print("📋 前64字节内容:")
        for i in range(0, min(64, len(self.data)), 16):
            hex_data = ' '.join(f'{b:02x}' for b in self.data[i:i+16])
            ascii_data = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in self.data[i:i+16])
            print(f"  {i:04x}: {hex_data:<48} |{ascii_data}|")
        
        # 检查常见的魔数
        magic_patterns = {
            b'\x7fELF': 'ELF文件',
            b'ANDROID!': 'Android Boot Image',
            b'\x00\x00\xa0\xe1': 'ARM代码',
            b'\x1f\x8b\x08': 'GZIP压缩',
            b'BZh': 'BZIP2压缩',
            b'\x89PNG': 'PNG图片',
        }
        
        print("\n🎯 魔数检测:")
        found_magic = False
        for magic, desc in magic_patterns.items():
            if self.data.startswith(magic):
                print(f"  ✅ 检测到: {desc}")
                found_magic = True
                break
        
        if not found_magic:
            print("  ⚠️  未检测到已知的文件魔数")
    
    def search_signature_patterns(self):
        """搜索可能的签名模式"""
        print("\n🔍 === 签名模式搜索 ===")
        
        # 搜索可能的签名长度值
        signature_lengths = [596, 1024, 2048, 4096]  # 常见的RSA签名长度
        
        for length in signature_lengths:
            # 小端序搜索
            pattern_le = struct.pack('<Q', length)
            positions = []
            start = 0
            while True:
                pos = self.data.find(pattern_le, start)
                if pos == -1:
                    break
                positions.append(pos)
                start = pos + 1
            
            if positions:
                print(f"🎯 找到签名长度 {length} (小端序) 在位置:")
                for pos in positions[:5]:  # 只显示前5个
                    print(f"   0x{pos:x} ({pos})")
                    # 显示周围的数据
                    start_show = max(0, pos - 16)
                    end_show = min(len(self.data), pos + 24)
                    hex_data = ' '.join(f'{b:02x}' for b in self.data[start_show:end_show])
                    print(f"   上下文: {hex_data}")
    
    def analyze_potential_header_locations(self):
        """分析可能的镜像头部位置"""
        print("\n🔍 === 潜在头部位置分析 ===")
        
        # 检查偏移0x30处是否有合理的大小值
        if len(self.data) >= 0x34:
            try:
                size_at_0x30 = struct.unpack('<I', self.data[0x30:0x34])[0]
                print(f"📏 偏移0x30处的值: {size_at_0x30} (0x{size_at_0x30:x})")
                
                if 0x1000 <= size_at_0x30 <= 0x1000000:  # 合理的镜像大小范围
                    print("  ✅ 看起来像是镜像大小字段")
                    
                    # 计算签名区域位置
                    sig_area = 512 + size_at_0x30
                    if sig_area < len(self.data):
                        print(f"📍 计算的签名区域偏移: 0x{sig_area:x}")
                        
                        # 检查签名长度字段位置
                        sig_len_offset = sig_area + 0x220
                        if sig_len_offset + 8 <= len(self.data):
                            sig_len = struct.unpack('<Q', self.data[sig_len_offset:sig_len_offset+8])[0]
                            print(f"🔍 签名长度字段值: {sig_len} (0x{sig_len:x})")
                            
                            if sig_len in [596, 1024, 2048, 4096]:
                                print("  ✅ 找到有效的签名长度字段！")
                                return sig_len_offset
                        else:
                            print("  ❌ 签名长度字段位置超出文件范围")
                    else:
                        print("  ❌ 计算的签名区域超出文件范围")
                else:
                    print("  ⚠️  值不在合理的镜像大小范围内")
            except:
                print("  ❌ 无法读取偏移0x30处的数据")
        
        return None
    
    def search_all_possible_locations(self):
        """搜索所有可能的签名字段位置"""
        print("\n🔍 === 全文搜索签名字段 ===")
        
        # 搜索596的所有出现位置
        target_value = 596
        pattern = struct.pack('<Q', target_value)
        
        positions = []
        start = 0
        while True:
            pos = self.data.find(pattern, start)
            if pos == -1:
                break
            positions.append(pos)
            start = pos + 1
        
        print(f"🎯 找到 {len(positions)} 个位置包含值 {target_value}:")
        for i, pos in enumerate(positions):
            print(f"\n位置 {i+1}: 0x{pos:x} ({pos})")
            
            # 显示周围32字节的数据
            start_show = max(0, pos - 16)
            end_show = min(len(self.data), pos + 16)
            
            print("  上下文数据:")
            for j in range(start_show, end_show, 16):
                hex_data = ' '.join(f'{b:02x}' for b in self.data[j:j+16])
                ascii_data = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in self.data[j:j+16])
                marker = "  >>> " if j <= pos < j + 16 else "      "
                print(f"{marker}{j:04x}: {hex_data:<48} |{ascii_data}|")
    
    def manual_modify_test(self):
        """手动修改测试"""
        print("\n🔧 === 手动修改测试 ===")
        
        # 搜索1024的位置（RSA-4096签名长度）
        target_1024 = struct.pack('<Q', 1024)
        pos_1024 = self.data.find(target_1024)
        
        if pos_1024 != -1:
            print(f"🎯 找到1024值在位置: 0x{pos_1024:x}")
            print("💡 建议修改步骤:")
            print(f"   1. 在偏移 0x{pos_1024:x} 处")
            print(f"   2. 将 '00 04 00 00 00 00 00 00' 改为 '54 02 00 00 00 00 00 00'")
            print(f"   3. 这会将1024改为596")
            
            # 生成修改命令
            print(f"\n📝 十六进制编辑器修改指令:")
            print(f"   偏移: {pos_1024} (0x{pos_1024:x})")
            print(f"   原值: 00 04 00 00 00 00 00 00")
            print(f"   新值: 54 02 00 00 00 00 00 00")
        else:
            print("❌ 未找到1024值，可能文件格式不同")
    
    def generate_fix_script(self):
        """生成修复脚本"""
        print("\n🛠️ === 生成修复脚本 ===")
        
        # 搜索1024值的位置
        target_1024 = struct.pack('<Q', 1024)
        pos_1024 = self.data.find(target_1024)
        
        if pos_1024 != -1:
            script_content = f'''#!/usr/bin/env python3
# 自动生成的UBOOT修改脚本

import struct

def fix_uboot(file_path):
    # 读取文件
    with open(file_path, 'rb') as f:
        data = bytearray(f.read())
    
    # 创建备份
    with open(file_path + '.backup', 'wb') as f:
        f.write(data)
    
    # 修改签名长度字段
    offset = 0x{pos_1024:x}
    new_value = struct.pack('<Q', 596)
    data[offset:offset+8] = new_value
    
    # 保存修改后的文件
    with open(file_path + '.fixed', 'wb') as f:
        f.write(data)
    
    print(f"✅ 修改完成！")
    print(f"📁 备份文件: {{file_path}}.backup")
    print(f"📁 修改文件: {{file_path}}.fixed")

if __name__ == "__main__":
    fix_uboot("{self.file_path}")
'''
            
            with open('fix_uboot.py', 'w') as f:
                f.write(script_content)
            
            print("✅ 已生成修复脚本: fix_uboot.py")
            print("🚀 运行命令: python fix_uboot.py")
        else:
            print("❌ 无法生成修复脚本，未找到目标值")
    
    def full_analysis(self):
        """完整分析"""
        print("🔍 开始完整分析...")
        
        if not self.load_file():
            return False
        
        self.analyze_header()
        self.search_signature_patterns()
        sig_offset = self.analyze_potential_header_locations()
        self.search_all_possible_locations()
        self.manual_modify_test()
        self.generate_fix_script()
        
        print("\n✅ 分析完成！")
        return True

def main():
    if len(sys.argv) != 2:
        print("使用方法: python uboot_analyzer.py <uboot_file>")
        return 1
    
    file_path = sys.argv[1]
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return 1
    
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║                UBOOT文件结构分析工具                         ║")
    print("║                                                              ║")
    print("║  功能: 深度分析UBOOT文件，定位签名字段位置                   ║")
    print("║  目标: 找到为什么修改后没有变化的原因                        ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print()
    
    analyzer = UBootAnalyzer(file_path)
    if analyzer.full_analysis():
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())
