#!/usr/bin/env python3
# 绕过尝试 5: 组合修改绕过
# 同时修改多个字段

import struct
import shutil

def bypass_attempt_5():
    file_path = r"F:\刷机桌面\过校验\dump\uboot"
    
    print("🚀 绕过尝试 5: 组合修改绕过")
    print("同时修改多个字段")
    
    # 备份
    shutil.copy2(file_path, file_path + '.backup_5')
    print("✅ 已创建备份")
    
    # 读取文件
    with open(file_path, 'rb') as f:
        data = bytearray(f.read())
    
    # 执行修改
    # 修改第一个字节设为2
    data[531536] = 2
    print(f"🔧 修改第一个字节设为2: {data[531536]} -> 2")
    
    # 保存
    with open(file_path + '.attempt_5', 'wb') as f:
        f.write(data)
    
    print("✅ 修改完成!")
    print(f"📁 输出文件: {file_path}.attempt_5")

if __name__ == "__main__":
    bypass_attempt_5()
