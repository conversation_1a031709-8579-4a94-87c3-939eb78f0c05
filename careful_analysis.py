#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仔细分析UBOOT文件结构
基于IDA Pro的精确分析，一步一步验证

使用方法: python careful_analysis.py <uboot_file>
"""

import struct
import sys
import os

def analyze_uboot_structure(file_path):
    """仔细分析UBOOT文件结构"""
    print("🔍 开始仔细分析UBOOT文件结构")
    print("=" * 60)
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        print(f"✅ 文件读取成功: {len(data)} 字节 (0x{len(data):x})")
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")
        return
    
    # 基于DHTBDump的已知信息
    known_info = {
        'file_size': 0x81ea4,      # 532132
        'payload_size': 0x819f0,   # 530928  
        'cert_size': 0x254,        # 596
        'cert_offset': 0x81c50,    # 531536
    }
    
    print(f"\n📋 已知信息 (来自DHTBDump):")
    print(f"文件大小: 0x{known_info['file_size']:x} ({known_info['file_size']})")
    print(f"载荷大小: 0x{known_info['payload_size']:x} ({known_info['payload_size']})")
    print(f"证书大小: 0x{known_info['cert_size']:x} ({known_info['cert_size']})")
    print(f"证书偏移: 0x{known_info['cert_offset']:x} ({known_info['cert_offset']})")
    
    # 分析镜像头部 (假设在文件开头)
    print(f"\n🔍 === 分析镜像头部 ===")
    
    if len(data) >= 0x34:
        # 读取偏移0x30处的镜像大小
        image_size = struct.unpack('<I', data[0x30:0x34])[0]
        print(f"偏移0x30处的值: 0x{image_size:x} ({image_size})")
        
        # 计算签名数据区域
        signature_area_offset = image_size + 512
        print(f"计算的签名区域偏移: 0x{signature_area_offset:x}")
        print(f"DHTBDump显示的证书偏移: 0x{known_info['cert_offset']:x}")
        
        if signature_area_offset == known_info['cert_offset']:
            print("✅ 签名区域偏移计算正确!")
        else:
            print("❌ 签名区域偏移计算不匹配")
            print("可能镜像头部不在文件开头，或者结构不同")
    
    # 分析签名数据区域
    print(f"\n🔍 === 分析签名数据区域 ===")
    cert_offset = known_info['cert_offset']
    
    if cert_offset + 0x230 <= len(data):
        # 读取关键偏移位置的值
        offsets_to_check = {
            0x218: "偏移536 - 签名数据指针",
            0x220: "偏移544 - 签名长度字段", 
            0x228: "偏移552 - 哈希数据指针"
        }
        
        for offset, description in offsets_to_check.items():
            abs_offset = cert_offset + offset
            if abs_offset + 8 <= len(data):
                value = struct.unpack('<Q', data[abs_offset:abs_offset+8])[0]
                print(f"{description}: 0x{value:x} ({value})")
                
                # 显示原始字节
                raw_bytes = ' '.join(f'{b:02x}' for b in data[abs_offset:abs_offset+8])
                print(f"  原始字节: {raw_bytes}")
            else:
                print(f"{description}: 超出文件范围")
    
    # 分析可能的绕过点
    print(f"\n💡 === 寻找可能的绕过点 ===")
    
    # 检查签名长度字段
    sig_len_offset = cert_offset + 0x220
    if sig_len_offset + 8 <= len(data):
        current_sig_len = struct.unpack('<Q', data[sig_len_offset:sig_len_offset+8])[0]
        print(f"当前签名长度: {current_sig_len}")
        
        if current_sig_len == 596:
            print("📋 这是RSA-3072签名")
            print("💡 可能的绕过: 修改为1024 (RSA-4096)")
            print(f"   位置: 0x{sig_len_offset:x}")
            print(f"   原值: 54 02 00 00 00 00 00 00")
            print(f"   新值: 00 04 00 00 00 00 00 00")
        elif current_sig_len == 1024:
            print("📋 这是RSA-4096签名")
            print("💡 可能的绕过: 修改为596 (RSA-3072)")
            print(f"   位置: 0x{sig_len_offset:x}")
            print(f"   原值: 00 04 00 00 00 00 00 00")
            print(f"   新值: 54 02 00 00 00 00 00 00")
        else:
            print(f"⚠️  未知的签名长度: {current_sig_len}")
    
    # 检查哈希数据指针
    hash_ptr_offset = cert_offset + 0x228
    if hash_ptr_offset + 8 <= len(data):
        hash_ptr = struct.unpack('<Q', data[hash_ptr_offset:hash_ptr_offset+8])[0]
        print(f"\n哈希数据指针: 0x{hash_ptr:x}")
        
        # 计算实际的哈希计算位置
        if current_sig_len == 596:
            hash_calc_offset = hash_ptr + 300
            print(f"RSA-3072哈希计算位置: 0x{hash_calc_offset:x}")
        else:
            hash_calc_offset = hash_ptr + 556
            print(f"RSA-4096哈希计算位置: 0x{hash_calc_offset:x}")
    
    # 搜索所有可能的签名长度值
    print(f"\n🔍 === 搜索所有签名长度值 ===")
    search_values = [596, 1024, 2048, 4096]
    
    for value in search_values:
        pattern_8 = struct.pack('<Q', value)
        pattern_4 = struct.pack('<I', value)
        
        # 搜索8字节模式
        pos = data.find(pattern_8)
        if pos != -1:
            print(f"找到{value} (8字节) 在位置: 0x{pos:x}")
            
        # 搜索4字节模式
        pos = data.find(pattern_4)
        if pos != -1:
            print(f"找到{value} (4字节) 在位置: 0x{pos:x}")

def generate_targeted_exploit(file_path):
    """生成针对性的漏洞利用脚本"""
    print(f"\n🛠️ === 生成针对性漏洞利用 ===")
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
    except:
        print("❌ 无法读取文件")
        return
    
    # 基于分析结果定位关键字段
    cert_offset = 0x81c50
    sig_len_offset = cert_offset + 0x220
    
    if sig_len_offset + 8 <= len(data):
        current_sig_len = struct.unpack('<Q', data[sig_len_offset:sig_len_offset+8])[0]
        
        if current_sig_len == 596:
            new_sig_len = 1024
            exploit_desc = "596 -> 1024 (RSA-3072 -> RSA-4096)"
        elif current_sig_len == 1024:
            new_sig_len = 596
            exploit_desc = "1024 -> 596 (RSA-4096 -> RSA-3072)"
        else:
            print(f"⚠️  未知签名长度: {current_sig_len}")
            return
        
        # 生成修改脚本
        script_content = f'''#!/usr/bin/env python3
# 针对性UBOOT签名绕过脚本
# 基于仔细分析生成

import struct
import shutil

def targeted_exploit():
    file_path = r"{file_path}"
    
    print("🚀 开始针对性签名绕过...")
    
    # 备份
    shutil.copy2(file_path, file_path + '.backup')
    print("✅ 已创建备份")
    
    # 读取文件
    with open(file_path, 'rb') as f:
        data = bytearray(f.read())
    
    # 修改签名长度字段
    offset = 0x{sig_len_offset:x}
    old_value = {current_sig_len}
    new_value = {new_sig_len}
    
    print(f"🔧 修改位置: 0x{{offset:x}}")
    print(f"📝 {exploit_desc}")
    
    # 执行修改
    data[offset:offset+8] = struct.pack('<Q', new_value)
    
    # 保存
    with open(file_path + '.exploited', 'wb') as f:
        f.write(data)
    
    print("✅ 修改完成!")
    print("📁 输出文件: " + file_path + '.exploited')
    
    # 验证
    with open(file_path + '.exploited', 'rb') as f:
        verify_data = f.read()
    verify_value = struct.unpack('<Q', verify_data[offset:offset+8])[0]
    
    if verify_value == new_value:
        print("✅ 修改验证成功!")
        print("💡 现在可以修改UBOOT代码的某些部分而绕过验证")
    else:
        print("❌ 修改验证失败!")

if __name__ == "__main__":
    targeted_exploit()
'''
        
        with open('targeted_exploit.py', 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print("🛠️ 已生成针对性利用脚本: targeted_exploit.py")
        print("🚀 运行命令: python targeted_exploit.py")

def main():
    if len(sys.argv) != 2:
        print("仔细分析UBOOT文件结构工具")
        print("=" * 40)
        print("使用方法:")
        print(f"  {sys.argv[0]} <uboot_file>")
        print()
        print("示例:")
        print(f'  {sys.argv[0]} "F:\\刷机桌面\\过校验\\dump\\uboot - 副本"')
        return 1
    
    file_path = sys.argv[1].strip('"\'')
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return 1
    
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║                仔细分析UBOOT文件结构工具                     ║")
    print("║                                                              ║")
    print("║  基于IDA Pro精确分析，一步一步验证文件结构                   ║")
    print("║  找到真正可以利用的绕过点                                    ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print()
    
    analyze_uboot_structure(file_path)
    generate_targeted_exploit(file_path)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
