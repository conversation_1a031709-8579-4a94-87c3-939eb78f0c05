package com.iapp.leochen.apkinjector;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.bottomnavigation.BottomNavigationView;

public class MainActivity extends AppCompatActivity {

    private ViewPager2 viewPager;
    private BottomNavigationView bottomNavigation;
    private ViewPagerAdapter viewPagerAdapter;

    // 双击退出相关
    private boolean doubleBackToExitPressedOnce = false;
    private Handler exitHandler = new Handler(Looper.getMainLooper());

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);

        initViews();
        setupViewPager();
        setupBottomNavigation();

        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
    }

    private void initViews() {
        viewPager = findViewById(R.id.viewPager);
        bottomNavigation = findViewById(R.id.bottomNavigation);
    }

    private void setupViewPager() {
        viewPagerAdapter = new ViewPagerAdapter(this);
        viewPager.setAdapter(viewPagerAdapter);

        // 禁用滑动切换，只允许通过底部导航切换
        viewPager.setUserInputEnabled(false);
    }

    private void setupBottomNavigation() {
        bottomNavigation.setOnItemSelectedListener(item -> {
            int itemId = item.getItemId();
            if (itemId == R.id.nav_home) {
                viewPager.setCurrentItem(0, false);
                return true;
            } else if (itemId == R.id.nav_about) {
                viewPager.setCurrentItem(1, false);
                return true;
            }
            return false;
        });

        // 默认选中主页
        bottomNavigation.setSelectedItemId(R.id.nav_home);
    }

    @Override
    public void onBackPressed() {
        // 如果当前在主页，尝试让HomeFragment处理返回键
        if (viewPager.getCurrentItem() == 0) {
            HomeFragment homeFragment = getHomeFragment();
            if (homeFragment != null && homeFragment.onBackPressed()) {
                // HomeFragment已处理返回键（导航到上级目录）
                return;
            }
        }

        // 如果不在主页，或者HomeFragment未处理返回键，执行双击退出逻辑
        handleDoubleBackToExit();
    }

    private HomeFragment getHomeFragment() {
        try {
            if (viewPagerAdapter != null) {
                return (HomeFragment) viewPagerAdapter.getFragment(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private void handleDoubleBackToExit() {
        if (doubleBackToExitPressedOnce) {
            // 第二次按返回键，退出应用
            super.onBackPressed();
            return;
        }

        // 第一次按返回键，显示提示
        this.doubleBackToExitPressedOnce = true;
        Toast.makeText(this, "再按一次返回键退出应用", Toast.LENGTH_SHORT).show();

        // 2秒后重置标志
        exitHandler.postDelayed(() -> doubleBackToExitPressedOnce = false, 2000);
    }
}