#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最简单的UBOOT签名字段搜索工具
直接搜索1024和596的十六进制模式

使用方法: python simple_search.py "f:\刷机桌面\过校验\dump\uboot"
"""

import sys
import os

def simple_search(file_path):
    """简单搜索签名字段"""
    print(f"🔍 搜索文件: {file_path}")
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        print(f"✅ 文件大小: {len(data)} 字节 (0x{len(data):x})")
    except Exception as e:
        print(f"❌ 读取失败: {e}")
        return
    
    # 搜索1024 (0x400) 的小端序表示: 00 04 00 00 00 00 00 00
    pattern_1024 = b'\x00\x04\x00\x00\x00\x00\x00\x00'
    
    # 搜索596 (0x254) 的小端序表示: 54 02 00 00 00 00 00 00  
    pattern_596 = b'\x54\x02\x00\x00\x00\x00\x00\x00'
    
    print("\n🎯 搜索 1024 (RSA-4096签名长度)...")
    pos = 0
    count = 0
    while True:
        pos = data.find(pattern_1024, pos)
        if pos == -1:
            break
        
        print(f"✅ 找到1024在位置: 0x{pos:x} ({pos})")
        
        # 显示周围32字节
        start = max(0, pos - 16)
        end = min(len(data), pos + 16)
        print("   周围数据:")
        for i in range(start, end, 16):
            hex_line = ' '.join(f'{b:02x}' for b in data[i:i+16])
            marker = " >>> " if i <= pos < i + 16 else "     "
            print(f"{marker}{i:04x}: {hex_line}")
        
        # 生成修改命令
        print(f"\n💡 修改建议 #{count+1}:")
        print(f"   位置: 0x{pos:x}")
        print(f"   原值: 00 04 00 00 00 00 00 00")
        print(f"   新值: 54 02 00 00 00 00 00 00")
        
        count += 1
        pos += 1
        
        if count >= 5:  # 最多显示5个
            break
    
    if count == 0:
        print("❌ 未找到1024值")
    else:
        print(f"\n📊 总共找到 {count} 个1024值的位置")
    
    print("\n🎯 搜索 596 (RSA-3072签名长度)...")
    pos = 0
    count_596 = 0
    while True:
        pos = data.find(pattern_596, pos)
        if pos == -1:
            break
        
        print(f"✅ 找到596在位置: 0x{pos:x} ({pos})")
        count_596 += 1
        pos += 1
        
        if count_596 >= 3:
            break
    
    if count_596 > 0:
        print(f"⚠️  文件中已存在596值，可能已经被修改过")
    
    # 如果找到1024，生成修改脚本
    if count > 0:
        generate_simple_fix(file_path, data, pattern_1024)

def generate_simple_fix(file_path, data, pattern_1024):
    """生成简单的修改脚本"""
    # 找到第一个1024的位置
    pos = data.find(pattern_1024)
    
    if pos == -1:
        return
    
    script = f'''#!/usr/bin/env python3
# 自动修改脚本 - 将1024改为596

def fix_uboot():
    file_path = r"{file_path}"
    
    # 读取文件
    with open(file_path, 'rb') as f:
        data = bytearray(f.read())
    
    # 备份
    with open(file_path + '.bak', 'wb') as f:
        f.write(data)
    print("✅ 已创建备份文件")
    
    # 修改位置 0x{pos:x}
    offset = 0x{pos:x}
    
    # 原值: 00 04 00 00 00 00 00 00 (1024)
    # 新值: 54 02 00 00 00 00 00 00 (596)
    data[offset:offset+8] = b'\\x54\\x02\\x00\\x00\\x00\\x00\\x00\\x00'
    
    # 保存修改后的文件
    with open(file_path + '.fixed', 'wb') as f:
        f.write(data)
    
    print("✅ 修改完成!")
    print(f"📁 原文件: {{file_path}}")
    print(f"📁 备份文件: {{file_path}}.bak")
    print(f"📁 修改文件: {{file_path}}.fixed")

if __name__ == "__main__":
    fix_uboot()
'''
    
    with open('fix_now.py', 'w', encoding='utf-8') as f:
        f.write(script)
    
    print(f"\n🛠️ 已生成修改脚本: fix_now.py")
    print("🚀 立即运行: python fix_now.py")

def main():
    if len(sys.argv) != 2:
        print('使用方法: python simple_search.py "文件路径"')
        print('\n示例:')
        print('  python simple_search.py "f:\\刷机桌面\\过校验\\dump\\uboot"')
        print('  python simple_search.py uboot.bin')
        return 1
    
    file_path = sys.argv[1]
    
    # 处理路径中的引号
    file_path = file_path.strip('"\'')
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        print("💡 请检查路径是否正确，注意使用双引号包围路径")
        return 1
    
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║                  简单签名字段搜索工具                        ║")
    print("║                                                              ║")
    print("║  直接搜索1024和596的十六进制模式                             ║")
    print("║  找到后自动生成修改脚本                                      ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print()
    
    simple_search(file_path)
    return 0

if __name__ == "__main__":
    sys.exit(main())
