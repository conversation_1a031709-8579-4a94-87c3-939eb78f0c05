package com.iapp.leochen.apkinjector;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

public class ViewPagerAdapter extends FragmentStateAdapter {

    private HomeFragment homeFragment;
    private AboutFragment aboutFragment;

    public ViewPagerAdapter(@NonNull FragmentActivity fragmentActivity) {
        super(fragmentActivity);
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        switch (position) {
            case 0:
                if (homeFragment == null) {
                    homeFragment = new HomeFragment();
                }
                return homeFragment;
            case 1:
                if (aboutFragment == null) {
                    aboutFragment = new AboutFragment();
                }
                return aboutFragment;
            default:
                if (homeFragment == null) {
                    homeFragment = new HomeFragment();
                }
                return homeFragment;
        }
    }

    @Override
    public int getItemCount() {
        return 2; // 两个页面：主页和关于
    }

    // 获取指定位置的Fragment
    public Fragment getFragment(int position) {
        switch (position) {
            case 0:
                return homeFragment;
            case 1:
                return aboutFragment;
            default:
                return homeFragment;
        }
    }
}
