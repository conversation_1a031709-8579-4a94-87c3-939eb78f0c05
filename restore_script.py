#!/usr/bin/env python3
# 恢复原始状态脚本 - 将596改为1024

import struct
import shutil

def restore_original():
    file_path = input("请输入UBOOT文件路径: ").strip('"')
    
    # 备份
    shutil.copy2(file_path, file_path + '.before_restore')
    print("✅ 已创建备份")
    
    # 读取文件
    with open(file_path, 'rb') as f:
        data = bytearray(f.read())
    
    # 修改位置 0x630ec
    offset = 0x630ec
    data[offset:offset+8] = struct.pack('<Q', 1024)
    
    # 保存
    with open(file_path + '.restored', 'wb') as f:
        f.write(data)
    
    print("✅ 恢复原始状态完成!")

if __name__ == "__main__":
    restore_original()
