#!/usr/bin/env python3
# 绕过尝试 4: 修改密钥长度绕过
# 修改密钥长度字段来改变验证逻辑

import struct
import shutil

def bypass_attempt_4():
    file_path = r"F:\刷机桌面\过校验\dump\uboot"
    
    print("🚀 绕过尝试 4: 修改密钥长度绕过")
    print("修改密钥长度字段来改变验证逻辑")
    
    # 备份
    shutil.copy2(file_path, file_path + '.backup_4')
    print("✅ 已创建备份")
    
    # 读取文件
    with open(file_path, 'rb') as f:
        data = bytearray(f.read())
    
    # 执行修改
    # 修改密钥长度
    data[531540:531544] = struct.pack('<I', 4096)
    print(f"🔧 修改密钥长度: 新值 4096")
    
    # 保存
    with open(file_path + '.attempt_4', 'wb') as f:
        f.write(data)
    
    print("✅ 修改完成!")
    print(f"📁 输出文件: {file_path}.attempt_4")

if __name__ == "__main__":
    bypass_attempt_4()
