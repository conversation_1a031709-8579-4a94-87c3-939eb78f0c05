#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深入分析RSA验证函数
寻找真正的绕过点

使用方法: python rsa_analysis.py <uboot_file>
"""

import struct
import sys
import os

def analyze_rsa_verification_data(file_path):
    """分析RSA验证相关的数据结构"""
    print("🔍 深入分析RSA验证数据结构")
    print("=" * 60)
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        print(f"✅ 文件读取成功: {len(data)} 字节")
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")
        return
    
    # 基于DHTBDump的信息
    cert_offset = 0x81c50
    
    print(f"📋 分析签名数据结构 (偏移: 0x{cert_offset:x})")
    
    if cert_offset + 600 <= len(data):
        # 分析RSA验证函数中检查的关键字段
        key_offsets = {
            0: "第一个字节 (v5) - 版本/类型字段",
            4: "偏移4 - 可能的密钥长度字段",
            556: "偏移556 - 检查值是否为1",
            588: "偏移588 - 另一个检查值是否为1", 
            568: "偏移568 - RSA验证数据起始",
            600: "偏移600 - 另一个RSA验证数据起始"
        }
        
        print(f"\n🔍 关键字段分析:")
        for offset, description in key_offsets.items():
            abs_offset = cert_offset + offset
            print(f"\n检查偏移 {offset} (绝对位置: 0x{abs_offset:x})")

            if abs_offset >= len(data):
                print(f"  ❌ 位置超出文件范围 (文件大小: {len(data)})")
                continue

            if offset == 0:
                # 第一个字节
                if abs_offset < len(data):
                    value = data[abs_offset]
                    print(f"  {description}: {value} (0x{value:02x})")
                    raw_bytes = f'{data[abs_offset]:02x}'
                    print(f"  原始字节: {raw_bytes}")
            else:
                # 4字节值
                if abs_offset + 4 <= len(data):
                    value = struct.unpack('<I', data[abs_offset:abs_offset+4])[0]
                    print(f"  {description}: {value} (0x{value:x})")
                    raw_bytes = ' '.join(f'{b:02x}' for b in data[abs_offset:abs_offset+4])
                    print(f"  原始字节: {raw_bytes}")
                else:
                    print(f"  ❌ 需要4字节但只剩 {len(data) - abs_offset} 字节")
        
        # 分析可能的绕过点
        print(f"\n💡 可能的绕过点分析:")
        
        # 安全地检查关键字段
        try:
            # 检查第一个字节
            if cert_offset < len(data):
                first_byte = data[cert_offset]
                print(f"1. 第一个字节当前值: {first_byte}")
                if first_byte == 0:
                    print("   - 当前走第二个分支 (v5 != 1)")
                    print("   - 可以尝试修改为1，走第一个分支")
                elif first_byte == 1:
                    print("   - 当前走第一个分支 (v5 == 1)")
                    print("   - 可以尝试修改为0，走第二个分支")
                else:
                    print(f"   - 当前值{first_byte} > 1，验证会直接失败")
                    print("   - 这可能就是绕过点！修改为0或1")
            else:
                print("1. ❌ 无法读取第一个字节")
                first_byte = 0

            # 检查偏移556和588的值
            if cert_offset + 560 <= len(data):
                val_556 = struct.unpack('<I', data[cert_offset + 556:cert_offset + 560])[0]
                print(f"2. 偏移556的值: {val_556}")
                print(f"   - RSA验证要求此值为1")
                print(f"   - 可以尝试修改为非1值来绕过")
            else:
                print("2. ❌ 无法读取偏移556的值")
                val_556 = 1

            if cert_offset + 592 <= len(data):
                val_588 = struct.unpack('<I', data[cert_offset + 588:cert_offset + 592])[0]
                print(f"3. 偏移588的值: {val_588}")
                print(f"   - RSA验证要求此值为1")
                print(f"   - 可以尝试修改为非1值来绕过")
            else:
                print("3. ❌ 无法读取偏移588的值")
                val_588 = 1

            # 检查密钥长度字段
            if cert_offset + 8 <= len(data):
                key_len = struct.unpack('<I', data[cert_offset + 4:cert_offset + 8])[0]
                print(f"4. 密钥长度字段: {key_len}")
                if key_len == 3072:
                    print("   - 当前是RSA-3072")
                    print("   - 可以尝试修改为其他值")
                elif key_len == 4096:
                    print("   - 当前是RSA-4096")
                    print("   - 可以尝试修改为其他值")
                else:
                    print(f"   - 未知密钥长度: {key_len}")
            else:
                print("4. ❌ 无法读取密钥长度字段")
                key_len = 3072

        except Exception as e:
            print(f"❌ 分析关键字段时出错: {e}")
            # 设置默认值
            first_byte = 0
            val_556 = 1
            val_588 = 1
            key_len = 3072

def generate_bypass_attempts(file_path):
    """生成多种绕过尝试"""
    print(f"\n🛠️ 生成多种绕过尝试")
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
    except:
        print("❌ 无法读取文件")
        return
    
    cert_offset = 0x81c50
    
    # 安全地读取当前关键值
    try:
        first_byte = data[cert_offset] if cert_offset < len(data) else 0
        val_556 = struct.unpack('<I', data[cert_offset + 556:cert_offset + 560])[0] if cert_offset + 560 <= len(data) else 1
        val_588 = struct.unpack('<I', data[cert_offset + 588:cert_offset + 592])[0] if cert_offset + 592 <= len(data) else 1
        key_len = struct.unpack('<I', data[cert_offset + 4:cert_offset + 8])[0] if cert_offset + 8 <= len(data) else 3072
    except:
        first_byte = 0
        val_556 = 1
        val_588 = 1
        key_len = 3072
    
    bypass_attempts = [
        {
            'name': '修改第一个字节绕过',
            'description': '修改版本/类型字段来改变验证分支',
            'modifications': [
                (cert_offset, 1 if first_byte != 1 else 0, '第一个字节')
            ]
        },
        {
            'name': '修改偏移556绕过',
            'description': '修改RSA验证检查字段',
            'modifications': [
                (cert_offset + 556, 0 if val_556 == 1 else 1, '偏移556')
            ]
        },
        {
            'name': '修改偏移588绕过',
            'description': '修改另一个RSA验证检查字段',
            'modifications': [
                (cert_offset + 588, 0 if val_588 == 1 else 1, '偏移588')
            ]
        },
        {
            'name': '修改密钥长度绕过',
            'description': '修改密钥长度字段来改变验证逻辑',
            'modifications': [
                (cert_offset + 4, 2048 if key_len != 2048 else 4096, '密钥长度')
            ]
        },
        {
            'name': '组合修改绕过',
            'description': '同时修改多个字段',
            'modifications': [
                (cert_offset, 2, '第一个字节设为2'),
            ]
        }
    ]
    
    for i, attempt in enumerate(bypass_attempts, 1):
        script_content = f'''#!/usr/bin/env python3
# 绕过尝试 {i}: {attempt['name']}
# {attempt['description']}

import struct
import shutil

def bypass_attempt_{i}():
    file_path = r"{file_path}"
    
    print("🚀 绕过尝试 {i}: {attempt['name']}")
    print("{attempt['description']}")
    
    # 备份
    shutil.copy2(file_path, file_path + '.backup_{i}')
    print("✅ 已创建备份")
    
    # 读取文件
    with open(file_path, 'rb') as f:
        data = bytearray(f.read())
    
    # 执行修改
'''
        
        for offset, new_value, desc in attempt['modifications']:
            if desc == '第一个字节' or desc == '第一个字节设为2':
                script_content += f'''    # 修改{desc}
    data[{offset}] = {new_value}
    print(f"🔧 修改{desc}: {{data[{offset}]}} -> {new_value}")
'''
            else:
                script_content += f'''    # 修改{desc}
    data[{offset}:{offset+4}] = struct.pack('<I', {new_value})
    print(f"🔧 修改{desc}: 新值 {new_value}")
'''
        
        script_content += f'''    
    # 保存
    with open(file_path + '.attempt_{i}', 'wb') as f:
        f.write(data)
    
    print("✅ 修改完成!")
    print(f"📁 输出文件: {{file_path}}.attempt_{i}")

if __name__ == "__main__":
    bypass_attempt_{i}()
'''
        
        with open(f'bypass_attempt_{i}.py', 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"🛠️ 已生成绕过尝试 {i}: bypass_attempt_{i}.py")
    
    print(f"\n🚀 使用方法:")
    for i in range(1, len(bypass_attempts) + 1):
        print(f"  python bypass_attempt_{i}.py")

def main():
    if len(sys.argv) != 2:
        print("深入分析RSA验证函数工具")
        print("=" * 40)
        print("使用方法:")
        print(f"  {sys.argv[0]} <uboot_file>")
        print()
        print("示例:")
        print(f'  {sys.argv[0]} "F:\\刷机桌面\\过校验\\dump\\uboot - 副本"')
        return 1
    
    file_path = sys.argv[1].strip('"\'')
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return 1
    
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║                深入分析RSA验证函数工具                       ║")
    print("║                                                              ║")
    print("║  分析RSA验证函数中的每个检查点                               ║")
    print("║  生成多种可能的绕过尝试                                      ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print()
    
    analyze_rsa_verification_data(file_path)
    generate_bypass_attempts(file_path)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
