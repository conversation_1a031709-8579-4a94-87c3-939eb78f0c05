#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
十六进制模式搜索工具
快速定位UBOOT文件中的签名字段

使用方法: python hex_finder.py <uboot_file>
"""

import struct
import sys
import os

def find_signature_field(file_path):
    """查找签名字段位置"""
    print(f"🔍 正在分析文件: {file_path}")
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        print(f"✅ 文件大小: {len(data)} 字节")
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return
    
    print("\n🎯 搜索常见签名长度值...")
    
    # 搜索目标值
    targets = {
        596: "RSA-3072签名长度",
        1024: "RSA-4096签名长度", 
        2048: "RSA-2048签名长度",
        4096: "可能的RSA密钥长度"
    }
    
    found_positions = []
    
    for value, desc in targets.items():
        # 小端序8字节
        pattern_8 = struct.pack('<Q', value)
        # 小端序4字节  
        pattern_4 = struct.pack('<I', value)
        # 大端序8字节
        pattern_8_be = struct.pack('>Q', value)
        # 大端序4字节
        pattern_4_be = struct.pack('>I', value)
        
        patterns = [
            (pattern_8, f"{desc} (8字节小端)"),
            (pattern_4, f"{desc} (4字节小端)"),
            (pattern_8_be, f"{desc} (8字节大端)"),
            (pattern_4_be, f"{desc} (4字节大端)")
        ]
        
        for pattern, pattern_desc in patterns:
            pos = 0
            count = 0
            while True:
                pos = data.find(pattern, pos)
                if pos == -1:
                    break
                
                if count == 0:
                    print(f"\n🎯 找到 {pattern_desc}:")
                
                print(f"   位置: 0x{pos:x} ({pos})")
                found_positions.append((pos, value, pattern_desc))
                
                # 显示周围数据
                start = max(0, pos - 8)
                end = min(len(data), pos + len(pattern) + 8)
                hex_data = ' '.join(f'{b:02x}' for b in data[start:end])
                print(f"   数据: {hex_data}")
                
                count += 1
                pos += 1
                
                if count >= 3:  # 最多显示3个位置
                    break
    
    if not found_positions:
        print("❌ 未找到常见的签名长度值")
        print("\n🔍 尝试搜索其他可能的值...")
        
        # 搜索一些可能的值
        possible_values = [256, 384, 512, 768, 1536, 3072]
        for value in possible_values:
            pattern = struct.pack('<Q', value)
            pos = data.find(pattern)
            if pos != -1:
                print(f"🎯 找到值 {value} 在位置 0x{pos:x}")
    
    print(f"\n📊 总共找到 {len(found_positions)} 个可能的位置")
    
    # 生成修改建议
    if found_positions:
        print("\n💡 修改建议:")
        for pos, value, desc in found_positions:
            if value == 1024:
                print(f"🎯 重点关注位置 0x{pos:x} - 这可能是需要修改的签名长度字段")
                print(f"   修改方法: 将此位置的8字节从 '00 04 00 00 00 00 00 00' 改为 '54 02 00 00 00 00 00 00'")
                
                # 生成具体的修改脚本
                generate_fix_script(file_path, pos)
                break

def generate_fix_script(file_path, offset):
    """生成修改脚本"""
    script_content = f'''#!/usr/bin/env python3
# 针对 {file_path} 的修改脚本

import struct
import shutil

def fix_signature():
    file_path = r"{file_path}"
    
    # 创建备份
    backup_path = file_path + ".backup"
    shutil.copy2(file_path, backup_path)
    print(f"✅ 已创建备份: {{backup_path}}")
    
    # 读取文件
    with open(file_path, 'rb') as f:
        data = bytearray(f.read())
    
    # 修改签名长度字段
    offset = 0x{offset:x}
    old_value = struct.unpack('<Q', data[offset:offset+8])[0]
    new_value = 596
    
    print(f"🔧 修改位置: 0x{{offset:x}}")
    print(f"📝 原值: {{old_value}}")
    print(f"📝 新值: {{new_value}}")
    
    # 执行修改
    data[offset:offset+8] = struct.pack('<Q', new_value)
    
    # 保存文件
    output_path = file_path.replace('.', '_fixed.')
    with open(output_path, 'wb') as f:
        f.write(data)
    
    print(f"✅ 修改完成: {{output_path}}")
    
    # 验证修改
    with open(output_path, 'rb') as f:
        verify_data = f.read()
    verify_value = struct.unpack('<Q', verify_data[offset:offset+8])[0]
    
    if verify_value == new_value:
        print("✅ 修改验证成功!")
    else:
        print("❌ 修改验证失败!")

if __name__ == "__main__":
    fix_signature()
'''
    
    with open('auto_fix.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"\n🛠️ 已生成自动修改脚本: auto_fix.py")
    print("🚀 运行命令: python auto_fix.py")

def main():
    if len(sys.argv) != 2:
        print("使用方法: python hex_finder.py <uboot_file>")
        print("\n示例:")
        print("  python hex_finder.py uboot")
        print("  python hex_finder.py f:\\刷机桌面\\过校验\\dump\\uboot")
        return 1
    
    file_path = sys.argv[1]
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return 1
    
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║                十六进制签名字段搜索工具                      ║")
    print("║                                                              ║")
    print("║  目标: 快速定位UBOOT文件中的签名长度字段                     ║")
    print("║  功能: 搜索常见签名长度值并生成修改脚本                      ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print()
    
    find_signature_field(file_path)
    return 0

if __name__ == "__main__":
    sys.exit(main())
