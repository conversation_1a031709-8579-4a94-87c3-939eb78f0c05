# 路径跳转功能实现

## 🎯 功能概述

将原来的"收藏"按钮改造为"路径跳转"功能，用户可以通过点击路径栏中的跳转按钮，快速导航到指定的文件夹路径。

## 🎨 界面设计

### 弹窗布局
- **标题**: "转跳路径"
- **当前路径显示**: 显示当前所在路径，单行显示，超出部分省略
- **转跳路径输入框**: Material3风格的输入框，支持多行输入
- **操作按钮**: "取消"和"转跳"两个按钮

### Material3风格特点
- 使用MaterialAlertDialogBuilder创建弹窗
- TextInputLayout.OutlinedBox风格的输入框
- Material3.Button样式的按钮
- 统一的颜色主题和圆角设计

## 🔧 技术实现

### 1. 布局文件

#### dialog_path_navigation.xml
```xml
<!-- 弹窗主布局 -->
<LinearLayout orientation="vertical" padding="24dp">
    <!-- 标题 -->
    <TextView text="转跳路径" />
    
    <!-- 当前路径显示 -->
    <TextView text="当前路径:" />
    <TextView id="currentPathDisplay" maxLines="1" ellipsize="end" />
    
    <!-- 转跳路径输入 -->
    <TextInputLayout hint="转跳路径">
        <TextInputEditText id="targetPathEditText" />
    </TextInputLayout>
    
    <!-- 操作按钮 -->
    <LinearLayout orientation="horizontal">
        <MaterialButton id="cancelButton" text="取消" />
        <MaterialButton id="navigateButton" text="转跳" />
    </LinearLayout>
</LinearLayout>
```

#### path_display_background.xml
```xml
<!-- 当前路径显示的背景 -->
<shape rectangle>
    <solid color="?attr/colorSurfaceVariant" />
    <corners radius="8dp" />
    <stroke width="1dp" color="?attr/colorOutlineVariant" />
</shape>
```

### 2. 图标资源

#### ic_navigation.xml
- 使用路径跳转相关的图标
- 支持主题色彩适配
- 18dp大小，适合按钮显示

### 3. Java实现

#### 核心方法

**showPathNavigationDialog()**
```java
private void showPathNavigationDialog() {
    // 1. 创建弹窗布局
    View dialogView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_path_navigation, null);
    
    // 2. 设置当前路径显示
    TextView currentPathDisplay = dialogView.findViewById(R.id.currentPathDisplay);
    currentPathDisplay.setText(currentDirectory.getAbsolutePath());
    
    // 3. 创建Material3弹窗
    MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(getContext())
            .setView(dialogView)
            .setCancelable(true);
    
    // 4. 设置按钮事件
    // 取消按钮：关闭弹窗
    // 转跳按钮：验证路径并导航
}
```

**navigateToPath(String targetPath)**
```java
private void navigateToPath(String targetPath) {
    try {
        File targetDirectory = new File(targetPath);
        
        // 路径验证
        if (!targetDirectory.exists()) {
            // 路径不存在
        }
        if (!targetDirectory.isDirectory()) {
            // 不是目录
        }
        if (!targetDirectory.canRead()) {
            // 没有读取权限
        }
        
        // 导航到目标路径
        currentDirectory = targetDirectory;
        loadFilesWithAnimation(null);
        
    } catch (Exception e) {
        // 错误处理
    }
}
```

## 🛡️ 安全验证

### 路径验证机制
1. **存在性检查**: 验证路径是否存在
2. **类型检查**: 确保是目录而非文件
3. **权限检查**: 验证是否有读取权限
4. **异常处理**: 捕获所有可能的异常

### 用户反馈
- **成功**: "已转跳到: /path/to/directory"
- **路径不存在**: "路径不存在: /invalid/path"
- **权限不足**: "没有访问权限: /restricted/path"
- **类型错误**: "路径不是目录: /file.txt"

## 🎮 用户体验

### 交互流程
1. **点击跳转按钮**: 显示路径跳转弹窗
2. **查看当前路径**: 弹窗顶部显示当前所在位置
3. **输入目标路径**: 在输入框中输入要跳转的路径
4. **确认跳转**: 点击"转跳"按钮执行导航
5. **取消操作**: 点击"取消"按钮关闭弹窗

### 便利性功能
- **路径复制**: 当前路径文本可选择复制
- **多行输入**: 支持长路径的多行显示
- **智能省略**: 当前路径超长时自动省略
- **即时反馈**: 操作结果立即显示Toast提示

## 📱 界面适配

### 响应式设计
- **弹窗宽度**: 自适应屏幕宽度，最大不超过屏幕
- **输入框高度**: 支持多行输入，最大3行
- **按钮布局**: 水平排列，右对齐

### 主题适配
- **浅色主题**: 使用系统浅色主题色彩
- **深色主题**: 自动适配深色主题
- **动态色彩**: 支持Material You动态色彩

## 🔄 与现有功能集成

### 动画系统兼容
- **无动画导航**: 路径跳转不使用滑动动画
- **直接加载**: 调用loadFilesWithAnimation(null)
- **状态保持**: 保持现有的动画状态管理

### 权限系统集成
- **权限检查**: 复用现有的权限检查机制
- **错误处理**: 统一的错误提示风格
- **用户引导**: 与权限请求界面保持一致

## 🎉 功能特点

### 核心优势
1. **快速导航**: 直接输入路径，无需逐级点击
2. **Material3设计**: 现代化的界面风格
3. **安全可靠**: 完善的路径验证机制
4. **用户友好**: 清晰的反馈和错误提示

### 使用场景
- **深层目录访问**: 快速跳转到深层嵌套的目录
- **常用路径**: 直接访问经常使用的路径
- **路径分享**: 通过复制粘贴快速导航
- **开发调试**: 开发者快速访问特定目录

现在用户可以通过点击路径栏中的跳转按钮，享受快速、安全、美观的路径导航体验！🚀
