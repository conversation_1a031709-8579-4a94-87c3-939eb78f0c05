#!/usr/bin/env python3
# 针对 f:\刷机桌面\过校验\dump\uboot 的修改脚本

import struct
import shutil

def fix_signature():
    file_path = r"f:\刷机桌面\过校验\dump\uboot"
    
    # 创建备份
    backup_path = file_path + ".backup"
    shutil.copy2(file_path, backup_path)
    print(f"✅ 已创建备份: {backup_path}")
    
    # 读取文件
    with open(file_path, 'rb') as f:
        data = bytearray(f.read())
    
    # 修改签名长度字段
    offset = 0x602d0
    old_value = struct.unpack('<Q', data[offset:offset+8])[0]
    new_value = 596
    
    print(f"🔧 修改位置: 0x{offset:x}")
    print(f"📝 原值: {old_value}")
    print(f"📝 新值: {new_value}")
    
    # 执行修改
    data[offset:offset+8] = struct.pack('<Q', new_value)
    
    # 保存文件
    output_path = file_path.replace('.', '_fixed.')
    with open(output_path, 'wb') as f:
        f.write(data)
    
    print(f"✅ 修改完成: {output_path}")
    
    # 验证修改
    with open(output_path, 'rb') as f:
        verify_data = f.read()
    verify_value = struct.unpack('<Q', verify_data[offset:offset+8])[0]
    
    if verify_value == new_value:
        print("✅ 修改验证成功!")
    else:
        print("❌ 修改验证失败!")

if __name__ == "__main__":
    fix_signature()
