<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:elevation="0dp"
    android:background="?attr/colorSurface">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="0dp"
        app:elevation="0dp"
        android:stateListAnimator="@animator/no_elevation"
        android:background="?attr/colorSurface"
        android:outlineProvider="none"
        android:translationZ="0dp">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="0dp"
            app:elevation="0dp"
            android:background="?attr/colorSurface"
            android:outlineProvider="none"
            android:translationZ="0dp"
            app:title="SRPatch"
            app:titleTextAppearance="@style/TextAppearance.Material3.HeadlineMedium" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Permission Request Layout -->
    <include
        android:id="@+id/permissionRequestLayout"
        layout="@layout/layout_permission_request"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    <!-- File Browser Layout -->
    <LinearLayout
        android:id="@+id/fileBrowserLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="visible"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- Fixed Top Section with Seamless Design -->
        <LinearLayout
            android:id="@+id/topSection"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="?attr/colorSurface"
            android:elevation="0dp"
            android:translationZ="0dp"
            android:stateListAnimator="@animator/no_elevation"
            android:outlineProvider="none">

            <!-- Current Path Card with Seamless Bottom -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginEnd="12dp"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="0dp"
                app:cardCornerRadius="10dp"
                app:cardElevation="0dp"
                app:strokeWidth="1dp"
                app:strokeColor="?attr/colorOutlineVariant"
                style="@style/Widget.Material3.CardView.Outlined">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="8dp"
                    android:gravity="center_vertical"
                    android:minHeight="44dp">

                    <ImageView
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:src="@drawable/ic_folder"
                        android:layout_marginEnd="6dp"
                        app:tint="?attr/colorPrimary" />

                    <TextView
                        android:id="@+id/currentPathText"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="/storage/emulated/0/"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="?attr/colorPrimary"
                        android:textSize="14sp"
                        android:clickable="true"
                        android:focusable="true"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:padding="2dp"
                        android:layout_marginStart="2dp"
                        android:layout_marginEnd="2dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/favoriteButton"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_marginEnd="2dp"
                        app:icon="@drawable/ic_bookmark_border"
                        app:iconSize="18dp"
                        style="@style/Widget.Material3.Button.IconButton" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/upButton"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        app:icon="@drawable/ic_arrow_upward"
                        app:iconSize="18dp"
                        style="@style/Widget.Material3.Button.IconButton" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Removed gradient separator to eliminate shadow effect -->

        </LinearLayout>

        <!-- Scrollable File List with Pull-to-Refresh -->
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipeRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <!-- File List Container with Smooth Parallel Animation -->
            <FrameLayout
                android:id="@+id/fileListContainer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:padding="16dp"
                android:background="?attr/colorSurface">

                <!-- Animation Container for Parallel Movement -->
                <FrameLayout
                    android:id="@+id/animationContainer"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="?attr/colorSurface">

                    <!-- Current RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/fileRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/colorSurface" />

                    <!-- Next RecyclerView (for animation) -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/fileRecyclerViewNext"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/colorSurface"
                        android:visibility="gone" />

                </FrameLayout>

                <!-- Loading Indicator -->
                <LinearLayout
                    android:id="@+id/loadingIndicator"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="32dp"
                    android:visibility="gone">

                    <ProgressBar
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="加载中..."
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

            </FrameLayout>

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
