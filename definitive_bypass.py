#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPL签名验证绕过 - 确定性修改脚本
基于IDA Pro分析的确切漏洞位置

漏洞原理：
RSA验证函数中偏移556处的检查字段控制验证严格程度
将此字段从1修改为0可以绕过严格的RSA验证

使用方法: python definitive_bypass.py
"""

import struct
import shutil
import os

def definitive_bypass():
    """确定性的SPL签名验证绕过"""
    
    # 文件路径
    file_path = r"F:\刷机桌面\过校验\dump\uboot"
    
    print("🚀 SPL签名验证绕过 - 确定性修改")
    print("=" * 50)
    print(f"目标文件: {file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        # 读取文件
        with open(file_path, 'rb') as f:
            data = bytearray(f.read())
        print(f"✅ 文件读取成功: {len(data)} 字节")
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")
        return False
    
    # 基于DHTBDump和IDA分析的确切位置
    cert_offset = 0x81c50      # 证书区域偏移
    target_offset = cert_offset + 556  # RSA验证检查字段偏移
    
    print(f"\n📍 关键信息:")
    print(f"证书区域偏移: 0x{cert_offset:x}")
    print(f"目标修改位置: 0x{target_offset:x}")
    
    # 检查位置是否有效
    if target_offset + 4 > len(data):
        print(f"❌ 目标位置超出文件范围")
        return False
    
    # 读取当前值
    current_value = struct.unpack('<I', data[target_offset:target_offset+4])[0]
    print(f"当前值: {current_value} (0x{current_value:x})")
    
    # 显示当前字节
    current_bytes = ' '.join(f'{b:02x}' for b in data[target_offset:target_offset+4])
    print(f"当前字节: {current_bytes}")
    
    # 创建备份
    backup_path = file_path + '.backup'
    try:
        shutil.copy2(file_path, backup_path)
        print(f"\n✅ 已创建备份: {backup_path}")
    except Exception as e:
        print(f"❌ 创建备份失败: {e}")
        return False
    
    # 执行修改
    print(f"\n🔧 执行漏洞利用:")
    print(f"修改位置: 0x{target_offset:x}")
    
    if current_value == 1:
        # 当前是1，修改为0来绕过验证
        new_value = 0
        print(f"修改方案: 1 -> 0 (绕过严格RSA验证)")
    elif current_value == 0:
        # 当前是0，可能已经被修改过，或者需要修改为1
        print(f"⚠️  当前值已经是0，可能已被修改过")
        print(f"尝试修改为1看看是否有效果")
        new_value = 1
    else:
        # 其他值，尝试修改为0
        print(f"当前值是{current_value}，尝试修改为0")
        new_value = 0
    
    # 执行修改
    data[target_offset:target_offset+4] = struct.pack('<I', new_value)
    
    # 保存修改后的文件
    output_path = file_path + '.bypassed'
    try:
        with open(output_path, 'wb') as f:
            f.write(data)
        print(f"✅ 修改后文件已保存: {output_path}")
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        return False
    
    # 验证修改
    try:
        with open(output_path, 'rb') as f:
            verify_data = f.read()
        verify_value = struct.unpack('<I', verify_data[target_offset:target_offset+4])[0]
        
        if verify_value == new_value:
            print(f"✅ 修改验证成功: {current_value} -> {new_value}")
        else:
            print(f"❌ 修改验证失败")
            return False
    except Exception as e:
        print(f"❌ 验证修改失败: {e}")
        return False
    
    # 显示修改结果
    print(f"\n🎉 漏洞利用完成!")
    print(f"📊 修改摘要:")
    print(f"   位置: 0x{target_offset:x}")
    print(f"   原值: {current_value}")
    print(f"   新值: {new_value}")
    print(f"   原理: 绕过RSA验证检查字段")
    
    print(f"\n📁 文件输出:")
    print(f"   原始文件: {file_path}")
    print(f"   备份文件: {backup_path}")
    print(f"   修改文件: {output_path}")
    
    print(f"\n💡 下一步:")
    print(f"1. 将修改后的文件刷入设备")
    print(f"2. 测试设备是否能正常启动")
    print(f"3. 如果成功，现在可以修改UBOOT代码而绕过验证")
    
    print(f"\n⚠️  重要提醒:")
    print(f"   - 这个修改针对RSA验证检查字段")
    print(f"   - 如果设备无法启动，使用备份文件恢复")
    print(f"   - 在测试环境中验证效果")
    
    return True

def show_alternative_methods():
    """显示其他可能的绕过方法"""
    print(f"\n🔄 如果上述方法无效，可尝试以下替代方案:")
    
    alternatives = [
        {
            'name': '修改第一个字节',
            'offset': 0x81c50,
            'description': '修改版本/类型字段改变验证分支',
            'modification': '将第一个字节修改为2 (>1会直接返回失败，但可能有其他逻辑)'
        },
        {
            'name': '修改偏移588字段',
            'offset': 0x81c50 + 588,
            'description': '修改另一个RSA验证检查字段',
            'modification': '将4字节值从1修改为0'
        },
        {
            'name': '修改签名长度字段',
            'offset': 0x81c50 + 544,
            'description': '操控哈希计算范围',
            'modification': '596<->1024互相修改'
        }
    ]
    
    for i, alt in enumerate(alternatives, 1):
        print(f"\n方案{i}: {alt['name']}")
        print(f"   位置: 0x{alt['offset']:x}")
        print(f"   原理: {alt['description']}")
        print(f"   修改: {alt['modification']}")

def main():
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║            SPL签名验证绕过 - 确定性修改工具                  ║")
    print("║                                                              ║")
    print("║  基于IDA Pro分析的确切漏洞位置                               ║")
    print("║  修改RSA验证检查字段绕过签名验证                             ║")
    print("║                                                              ║")
    print("║  ⚠️  仅用于安全研究和授权测试！                             ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print()
    
    if definitive_bypass():
        print("\n🎯 主要绕过方法执行完成!")
        show_alternative_methods()
        return 0
    else:
        print("\n💥 主要绕过方法失败!")
        show_alternative_methods()
        return 1

if __name__ == "__main__":
    main()
