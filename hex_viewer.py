#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的十六进制查看工具
查看UBOOT文件中关键位置的数据

使用方法: python hex_viewer.py <uboot_file>
"""

import struct
import sys
import os

def view_hex_data(file_path):
    """查看关键位置的十六进制数据"""
    print("🔍 十六进制数据查看器")
    print("=" * 60)
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        print(f"✅ 文件读取成功: {len(data)} 字节 (0x{len(data):x})")
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")
        return
    
    # 基于DHTBDump的证书偏移
    cert_offset = 0x81c50
    
    print(f"\n📋 证书区域起始位置: 0x{cert_offset:x}")
    
    # 显示证书区域的前128字节
    print(f"\n🔍 证书区域前128字节:")
    if cert_offset + 128 <= len(data):
        for i in range(0, 128, 16):
            abs_pos = cert_offset + i
            line_data = data[abs_pos:abs_pos+16]
            hex_str = ' '.join(f'{b:02x}' for b in line_data)
            ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in line_data)
            print(f"  {abs_pos:08x}: {hex_str:<48} |{ascii_str}|")
    else:
        print("  ❌ 证书区域超出文件范围")
    
    # 检查关键偏移位置
    key_positions = [
        (0, "第一个字节 (版本/类型)"),
        (4, "密钥长度字段"),
        (544, "签名长度字段"),
        (552, "哈希数据指针"),
        (556, "RSA验证检查字段1"),
        (588, "RSA验证检查字段2"),
    ]
    
    print(f"\n🎯 关键位置数据:")
    for offset, description in key_positions:
        abs_pos = cert_offset + offset
        print(f"\n{description} (偏移 {offset}, 位置 0x{abs_pos:x}):")
        
        if abs_pos + 8 <= len(data):
            # 显示8字节数据
            data_8 = data[abs_pos:abs_pos+8]
            hex_8 = ' '.join(f'{b:02x}' for b in data_8)
            print(f"  8字节: {hex_8}")
            
            # 解释为不同的数据类型
            if offset == 0:
                print(f"  作为字节: {data[abs_pos]}")
            else:
                val_32 = struct.unpack('<I', data[abs_pos:abs_pos+4])[0]
                val_64 = struct.unpack('<Q', data[abs_pos:abs_pos+8])[0]
                print(f"  作为32位整数: {val_32} (0x{val_32:x})")
                print(f"  作为64位整数: {val_64} (0x{val_64:x})")
        else:
            print(f"  ❌ 位置超出文件范围")
    
    # 搜索特定的值
    print(f"\n🔍 搜索特定值:")
    search_values = [
        (596, "RSA-3072签名长度"),
        (1024, "RSA-4096签名长度"),
        (3072, "RSA-3072密钥长度"),
        (4096, "RSA-4096密钥长度"),
    ]
    
    for value, description in search_values:
        # 搜索32位小端序
        pattern_32 = struct.pack('<I', value)
        pos = data.find(pattern_32)
        if pos != -1:
            print(f"  找到{description} (32位): 位置 0x{pos:x}")
        
        # 搜索64位小端序
        pattern_64 = struct.pack('<Q', value)
        pos = data.find(pattern_64)
        if pos != -1:
            print(f"  找到{description} (64位): 位置 0x{pos:x}")

def suggest_modifications(file_path):
    """基于数据分析建议修改方案"""
    print(f"\n💡 修改建议:")
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
    except:
        print("❌ 无法读取文件")
        return
    
    cert_offset = 0x81c50
    
    # 检查第一个字节
    if cert_offset < len(data):
        first_byte = data[cert_offset]
        print(f"\n1. 第一个字节修改 (当前值: {first_byte}):")
        if first_byte == 0:
            print(f"   修改为: 1 (切换到另一个验证分支)")
        elif first_byte == 1:
            print(f"   修改为: 0 (切换到另一个验证分支)")
        else:
            print(f"   修改为: 0 或 1 (当前值{first_byte}可能导致验证失败)")
    
    # 检查签名长度字段
    if cert_offset + 552 <= len(data):
        sig_len = struct.unpack('<Q', data[cert_offset + 544:cert_offset + 552])[0]
        print(f"\n2. 签名长度字段修改 (当前值: {sig_len}):")
        if sig_len == 596:
            print(f"   修改为: 1024 (RSA-3072 -> RSA-4096)")
        elif sig_len == 1024:
            print(f"   修改为: 596 (RSA-4096 -> RSA-3072)")
        else:
            print(f"   修改为: 596 或 1024")
    
    # 检查RSA验证字段
    if cert_offset + 560 <= len(data):
        val_556 = struct.unpack('<I', data[cert_offset + 556:cert_offset + 560])[0]
        print(f"\n3. RSA验证字段1修改 (当前值: {val_556}):")
        if val_556 == 1:
            print(f"   修改为: 0 (绕过RSA验证检查)")
        else:
            print(f"   修改为: 1 (启用RSA验证检查)")

def main():
    if len(sys.argv) != 2:
        print("十六进制数据查看工具")
        print("=" * 30)
        print("使用方法:")
        print(f"  {sys.argv[0]} <uboot_file>")
        print()
        print("示例:")
        print(f'  {sys.argv[0]} "F:\\刷机桌面\\过校验\\dump\\uboot"')
        return 1
    
    file_path = sys.argv[1].strip('"\'')
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return 1
    
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║                  十六进制数据查看工具                        ║")
    print("║                                                              ║")
    print("║  查看UBOOT文件中关键位置的十六进制数据                       ║")
    print("║  分析可能的修改点                                            ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print()
    
    view_hex_data(file_path)
    suggest_modifications(file_path)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
