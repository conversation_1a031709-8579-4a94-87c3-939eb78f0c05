<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="32dp">

    <!-- App Icon -->
    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@mipmap/ic_launcher"
        android:layout_marginBottom="32dp" />

    <!-- Title -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="需要存储权限"
        android:textAppearance="@style/TextAppearance.Material3.HeadlineMedium"
        android:textColor="?attr/colorOnSurface"
        android:layout_marginBottom="16dp" />

    <!-- Description -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="SRPatch需要访问设备存储权限来浏览和管理文件。请授予所有文件访问权限以继续使用。"
        android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
        android:textColor="?attr/colorOnSurfaceVariant"
        android:gravity="center"
        android:layout_marginBottom="32dp" />

    <!-- Grant Permission Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/grantPermissionButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="开始授权"
        android:textSize="16sp"
        android:paddingHorizontal="32dp"
        android:paddingVertical="12dp"
        style="@style/Widget.Material3.Button.UnelevatedButton" />

</LinearLayout>
