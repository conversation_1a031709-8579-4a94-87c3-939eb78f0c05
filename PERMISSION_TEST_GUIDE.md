# 权限功能测试指南

## 测试环境
- 建议在Android 11+设备上测试（特别是Android 15）
- 确保设备支持"所有文件访问权限"设置

## 测试步骤

### 1. 首次安装测试
1. 安装应用到设备
2. 首次打开应用
3. **预期结果**: 应该显示权限授权界面，包含：
   - 应用图标（居中显示）
   - "需要存储权限"标题
   - 权限说明文字
   - "开始授权"按钮

### 2. 权限授权测试
1. 点击"开始授权"按钮
2. **预期结果**: 自动跳转到系统设置页面
   - Android 11+: 跳转到"所有文件访问权限"设置页面
   - Android 10-: 弹出权限请求对话框

### 3. 权限授予测试
1. 在系统设置中授予权限
2. 返回应用
3. **预期结果**: 
   - 权限授权界面自动消失
   - 显示文件浏览界面
   - 自动加载/storage/emulated/0/目录内容

### 4. 权限撤销测试
1. 在应用中正常浏览文件
2. 切换到系统设置，撤销权限
3. 返回应用
4. **预期结果**: 
   - 文件浏览界面消失
   - 重新显示权限授权界面

### 5. 应用重启测试
1. 授予权限后正常使用应用
2. 完全关闭应用
3. 重新打开应用
4. **预期结果**: 
   - 直接显示文件浏览界面
   - 不再显示权限授权界面

## 常见问题排查

### 问题1: 点击授权按钮无反应
- 检查设备是否支持MANAGE_EXTERNAL_STORAGE权限
- 查看logcat日志确认是否有异常

### 问题2: 授权后界面不刷新
- 检查onResume方法是否正常调用
- 确认权限检测逻辑是否正确

### 问题3: 在某些设备上跳转失败
- 应用会自动fallback到通用权限设置页面
- 用户需要手动找到应用权限设置

## 测试通过标准
✅ 权限授权界面正确显示
✅ 点击授权按钮能正确跳转
✅ 授权后界面自动刷新
✅ 权限撤销后正确显示授权界面
✅ 应用重启后权限状态正确保持
